@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔍 Daikin AI System - System Status Check
echo ========================================
echo.

:: Set color
color 0C

:: Switch to project root directory
cd /d "%~dp0\.."

echo 📋 Checking system status and dependencies...
echo.

:: Check Node.js
echo 🟢 Checking Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js installed
    node --version
) else (
    echo ❌ Node.js not installed
    echo Please download and install from https://nodejs.org
)

:: Check npm
echo.
echo 🟢 Checking npm...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm installed
    npm --version
) else (
    echo ❌ npm not installed
)

:: Check project dependencies
echo.
echo 🟢 Checking project dependencies...
if exist "node_modules" (
    echo ✅ Project dependencies installed
    echo Number of dependency packages:
    dir /b node_modules | find /c /v "" 
) else (
    echo ❌ Project dependencies not installed
    echo Please run: npm install
)

:: Check package.json
echo.
echo 🟢 Checking project configuration...
if exist "package.json" (
    echo ✅ package.json exists
    echo Project information:
    findstr "name\|version" package.json
) else (
    echo ❌ package.json does not exist
)

:: Check Ollama
echo.
echo 🟢 Checking Ollama service...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Ollama service is running
    echo Port: 11434
) else (
    echo ❌ Ollama service not running
    echo Please run: ollama serve
)

:: Check Ollama model
echo.
echo 🟢 Checking AI model...
ollama list >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Ollama command available
    echo Installed models:
    ollama list | findstr "llama3.1:8b"
    if %errorlevel% equ 0 (
        echo ✅ llama3.1:8b model installed
    ) else (
        echo ❌ llama3.1:8b model not installed
        echo Please run: ollama pull llama3.1:8b
    )
) else (
    echo ❌ Ollama command not available
    echo Please install Ollama: https://ollama.ai
)

:: Check development server
echo.
echo 🟢 Checking development server...
curl -s http://localhost:5173 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Development server is running
    echo Address: http://localhost:5173
) else (
    echo ❌ Development server not running
    echo Please run: npm run dev
)

:: Check port usage
echo.
echo 🟢 Checking port status...
netstat -ano | findstr ":5173" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Port 5173 is occupied
    netstat -ano | findstr ":5173"
) else (
    echo ⚠️  Port 5173 is free
)

netstat -ano | findstr ":11434" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Port 11434 is occupied (Ollama)
    netstat -ano | findstr ":11434"
) else (
    echo ⚠️  Port 11434 is free
)

:: Check system resources
echo.
echo 🟢 Checking system resources...
echo CPU usage:
wmic cpu get loadpercentage /value | findstr "LoadPercentage"

echo.
echo Memory usage:
for /f "skip=1" %%p in ('wmic os get TotalVisibleMemorySize^,FreePhysicalMemory /format:csv') do (
    for /f "tokens=2,3 delims=," %%a in ("%%p") do (
        set /a "total=%%a/1024"
        set /a "free=%%b/1024"
        set /a "used=total-free"
        echo Total memory: !total! MB
        echo Used memory: !used! MB
        echo Available memory: !free! MB
    )
)

:: Check disk space
echo.
echo Disk space:
for /f "tokens=3,4" %%a in ('dir /-c') do (
    if "%%b"=="bytes" (
        set /a "free_space=%%a/1024/1024"
        echo Available space: !free_space! MB
    )
)

:: Generate system report
echo.
echo ========================================
echo 📊 System Status Summary
echo ========================================

:: Calculate readiness status
set ready_count=0
set total_checks=8

:: Node.js check
node --version >nul 2>&1
if %errorlevel% equ 0 set /a ready_count+=1

:: npm check
npm --version >nul 2>&1
if %errorlevel% equ 0 set /a ready_count+=1

:: Dependencies check
if exist "node_modules" set /a ready_count+=1

:: package.json check
if exist "package.json" set /a ready_count+=1

:: Ollama service check
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% equ 0 set /a ready_count+=1

:: Model check
ollama list | findstr "llama3.1:8b" >nul 2>&1
if %errorlevel% equ 0 set /a ready_count+=1

:: Development server check
curl -s http://localhost:5173 >nul 2>&1
if %errorlevel% equ 0 set /a ready_count+=1

:: Port check
netstat -ano | findstr ":5173" >nul 2>&1
if %errorlevel% equ 0 set /a ready_count+=1

set /a ready_percent=ready_count*100/total_checks

echo.
echo 🎯 System Readiness Status: %ready_count%/%total_checks% (%ready_percent%%%)
echo.

if %ready_count% equ %total_checks% (
    echo ✅ System fully ready, can start demonstration!
    echo.
    echo 🚀 Recommended next steps:
    echo   1. Run quick demo: quick-demo.bat
    echo   2. Run complete tests: run-tests.bat
    echo   3. View test guide: TESTING_GUIDE.md
) else (
    echo ⚠️  System not fully ready, please resolve the following issues:
    echo.
    
    node --version >nul 2>&1
    if %errorlevel% neq 0 echo   • Install Node.js
    
    npm --version >nul 2>&1
    if %errorlevel% neq 0 echo   • Install npm
    
    if not exist "node_modules" echo   • Run npm install
    
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if %errorlevel% neq 0 echo   • Start Ollama service: ollama serve
    
    ollama list | findstr "llama3.1:8b" >nul 2>&1
    if %errorlevel% neq 0 echo   • Download AI model: ollama pull llama3.1:8b
    
    curl -s http://localhost:5173 >nul 2>&1
    if %errorlevel% neq 0 echo   • Start development server: npm run dev
)

echo.
echo ========================================
echo 📋 Check Complete
echo ========================================
echo.

pause
