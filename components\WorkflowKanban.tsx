
import React, { useMemo, useContext } from 'react';
import type { Ticket, TicketStatus, View } from '../types';
import { AppContext } from './AppContext';

const AssigneeAvatar: React.FC<{ userId: string }> = ({ userId }) => {
  const { users } = useContext(AppContext);
  const user = users.find(u => u.id === userId);
  return user ? (
    <div title={user.name} className="h-7 w-7 rounded-full bg-gray-300 border-2 border-white flex items-center justify-center text-xs font-bold text-gray-700">
      {user.avatar}
    </div>
  ) : null;
};

const TicketCard: React.FC<{ ticket: Ticket; onDragStart: (e: React.DragEvent<HTMLDivElement>, id: string) => void; onClick: () => void; }> = ({ ticket, onDragStart, onClick }) => (
  <div
    draggable
    onDragStart={(e) => onDragStart(e, ticket.id)}
    onClick={onClick}
    className="bg-white p-4 rounded-lg border border-gray-200 cursor-pointer active:cursor-grabbing hover:border-blue-500 hover:shadow-md transition-shadow"
  >
    <h4 className="font-bold text-gray-800 mb-2 text-sm">{ticket.title}</h4>
    <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
            <span className="text-xs font-semibold px-2 py-0.5 bg-gray-100 text-gray-700 rounded">{ticket.category}</span>
            <span className={`text-xs font-semibold px-2 py-0.5 rounded ${ticket.urgency === 'High' ? 'bg-red-100 text-red-800' : ticket.urgency === 'Medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                {ticket.urgency}
            </span>
        </div>
        <div className="flex items-center -space-x-2">
            {ticket.assignees.map(id => <AssigneeAvatar key={id} userId={id} />)}
        </div>
    </div>
  </div>
);

const TicketColumn: React.FC<{ 
    title: TicketStatus; 
    tickets: Ticket[]; 
    onDragStart: (e: React.DragEvent<HTMLDivElement>, id: string) => void;
    onDrop: (e: React.DragEvent<HTMLDivElement>, status: TicketStatus) => void;
    onCardClick: (id: string) => void;
}> = ({ title, tickets, onDragStart, onDrop, onCardClick }) => {
  const onDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const statusColors: Record<string, string> = {
    'Received': 'text-blue-600 border-blue-500',
    'In Progress': 'text-violet-600 border-violet-500',
    'Waiting Feedback': 'text-orange-600 border-orange-500',
    'Done': 'text-green-600 border-green-500',
    'Canceled': 'text-gray-500 border-gray-400',
    'Closed': 'text-gray-500 border-gray-400',
    'Reopened': 'text-pink-600 border-pink-500'
  }

  return (
    <div 
        className="flex-1 bg-gray-100 rounded-lg p-3 min-w-[320px] flex flex-col"
        onDrop={(e) => onDrop(e, title)}
        onDragOver={onDragOver}
    >
      <div className={`flex items-center justify-between mb-4 pb-2 border-b-2 ${statusColors[title]}`}>
        <h3 className={`font-semibold text-sm flex items-center gap-2`}>
            <span className={`w-2.5 h-2.5 rounded-full ${statusColors[title].replace('text-','bg-').replace('/400','/500')}`}></span>
            {title}
        </h3>
        <span className="text-sm font-bold text-gray-500 bg-gray-200 w-6 h-6 flex items-center justify-center rounded-full">{tickets.length}</span>
      </div>
      <div className="space-y-3 h-full overflow-y-auto pr-1">
        {tickets.map(ticket => <TicketCard key={ticket.id} ticket={ticket} onDragStart={onDragStart} onClick={() => onCardClick(ticket.id)} />)}
      </div>
    </div>
  );
};

const KanbanBoard: React.FC<{ 
    setActiveView: (view: View) => void;
}> = ({ setActiveView }) => {
  const { tickets, updateTicket } = useContext(AppContext);
  
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, id: string) => {
    e.dataTransfer.setData('ticketId', id);
  };
  
  const handleDrop = (e: React.DragEvent<HTMLDivElement>, newStatus: TicketStatus) => {
    const id = e.dataTransfer.getData('ticketId');
    const ticketToUpdate = tickets.find(t => t.id === id);
    if (ticketToUpdate) {
      updateTicket({ ...ticketToUpdate, status: newStatus });
    }
  };

  const handleCardClick = (id: string) => {
    setActiveView(`detail-${id}`);
  };

  const columns = useMemo(() => {
    return tickets.reduce((acc, ticket) => {
      (acc[ticket.status] = acc[ticket.status] || []).push(ticket);
      return acc;
    }, {} as Record<TicketStatus, Ticket[]>);
  }, [tickets]);

  const statuses: TicketStatus[] = ['Received', 'In Progress', 'Waiting Feedback', 'Done', 'Reopened', 'Canceled', 'Closed'];

  return (
     <div className="bg-gray-50 flex-1 flex flex-col overflow-hidden">
        <div className="flex-1 flex gap-4 p-4 overflow-x-auto">
            {statuses.map(status => (
                <TicketColumn 
                    key={status}
                    title={status} 
                    tickets={columns[status] || []} 
                    onDragStart={handleDragStart} 
                    onDrop={handleDrop}
                    onCardClick={handleCardClick}
                />
            ))}
        </div>
    </div>
  );
};

export default KanbanBoard;