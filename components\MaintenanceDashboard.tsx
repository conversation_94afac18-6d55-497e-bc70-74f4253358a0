
import React, { useState, useMemo, useContext, useRef, ChangeEvent, useEffect } from 'react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { ExclamationTriangleIcon, PlusIcon, UploadIcon, PencilIcon, XCircleIcon, ChartBarIcon, CpuChipIcon } from './icons/Icons';
import { AppContext } from './AppContext';
import type { Machine, SensorData, Alert } from '../types';
import PredictiveAnalysisPanel from './PredictiveAnalysisPanel';

// --- Reusable UI Components ---

const KPICard: React.FC<{ title: string; value: number | string; unit?: string }> = ({ title, value, unit }) => (
    <div className="bg-white p-4 rounded-lg border border-gray-200">
        <p className="text-sm text-gray-500">{title}</p>
        <p className="text-2xl font-bold text-gray-900">{value} <span className="text-lg text-gray-400">{unit}</span></p>
    </div>
);

const SignalTrendChart: React.FC<{ data: SensorData[], dataKey: string, stroke: string, name: string }> = ({ data, dataKey, stroke, name }) => (
    <ResponsiveContainer width="100%" height={150}>
        <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="rgba(0, 0, 0, 0.05)" />
            <XAxis dataKey="timestamp" tickFormatter={(ts) => new Date(ts).toLocaleTimeString()} stroke="#9ca3af" fontSize={12} tick={{ fill: '#9ca3af' }} />
            <YAxis stroke="#9ca3af" fontSize={12} tick={{ fill: '#9ca3af' }}/>
            <Tooltip contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderColor: '#e5e7eb', color: '#1f2937' }} />
            <Area type="monotone" dataKey="value" stroke={stroke} fill={stroke} fillOpacity={0.2} name={name} />
        </AreaChart>
    </ResponsiveContainer>
);

const StatusDot: React.FC<{status: Machine['status']}> = ({ status }) => (
    <span className={`w-2.5 h-2.5 rounded-full ${status === 'Operational' ? 'bg-green-500' : status === 'Warning' ? 'bg-yellow-400' : 'bg-red-500'}`}></span>
);

const TabButton: React.FC<{ label: string; icon: React.ReactNode; active: boolean; onClick: () => void }> = ({ label, icon, active, onClick }) => (
    <button
        onClick={onClick}
        className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium text-sm transition-colors ${
            active
                ? 'bg-blue-600 text-white'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
        }`}
    >
        {icon}
        {label}
    </button>
);

// --- Main Dashboard Component ---

const MaintenanceDashboard: React.FC = () => {
    const { machines, alerts, addMachine, updateMachine, deleteMachine, addMachinesFromCSV } = useContext(AppContext);
    const [selectedMachineId, setSelectedMachineId] = useState<string | null>(machines.length > 0 ? machines[0].id : null);
    const [activeTab, setActiveTab] = useState<'monitoring' | 'predictive'>('monitoring');
    
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingMachine, setEditingMachine] = useState<Machine | null>(null);

    const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
    const [machineToDelete, setMachineToDelete] = useState<Machine | null>(null);

    const fileInputRef = useRef<HTMLInputElement>(null);

    const selectedMachine = useMemo(() => machines.find(m => m.id === selectedMachineId), [machines, selectedMachineId]);

    const kpi = useMemo(() => {
        const operationalCount = machines.filter(m => m.status === 'Operational').length;
        const uptime = machines.length > 0 ? `${((operationalCount / machines.length) * 100).toFixed(1)}%` : '100%';
        return {
            active: machines.length,
            faults: machines.filter(m => m.status === 'Failure').length,
            warnings: machines.filter(m => m.status === 'Warning').length,
            uptime: uptime,
        }
    }, [machines]);
    
    useEffect(() => {
      if (!selectedMachineId && machines.length > 0) {
        setSelectedMachineId(machines[0].id);
      }
      if (selectedMachineId && !machines.some(m => m.id === selectedMachineId)) {
        setSelectedMachineId(machines.length > 0 ? machines[0].id : null);
      }
    }, [machines, selectedMachineId]);


    const handleAddClick = () => {
        setEditingMachine(null);
        setIsModalOpen(true);
    };

    const handleEditClick = (machine: Machine) => {
        setEditingMachine(machine);
        setIsModalOpen(true);
    };

    const handleDeleteClick = (machine: Machine) => {
        setMachineToDelete(machine);
        setIsDeleteConfirmOpen(true);
    }
    
    const confirmDelete = () => {
        if(machineToDelete) {
            deleteMachine(machineToDelete.id);
        }
        setIsDeleteConfirmOpen(false);
        setMachineToDelete(null);
    }

    const handleSaveMachine = (machineData: any) => {
        if (editingMachine) {
            updateMachine({ ...editingMachine, ...machineData });
        } else {
            addMachine(machineData);
        }
        setIsModalOpen(false);
    };

    const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            const text = e.target?.result as string;
            addMachinesFromCSV(text);
        };
        reader.readAsText(file);
        // Reset file input to allow uploading the same file again
        if(event.target) event.target.value = '';
    };

    // Pagination for alerts
    const [alertsPage, setAlertsPage] = useState(0);
    const alertsPerPage = 4;
    const displayedAlerts = useMemo(() => {
        const start = alertsPage * alertsPerPage;
        return alerts.slice(start, start + alertsPerPage);
    }, [alerts, alertsPage]);

    const totalAlertsPages = Math.ceil(alerts.length / alertsPerPage);

    return (
        <div className="flex-1 flex flex-col text-gray-800 bg-gray-100 overflow-hidden">
            {/* Header */}
            <header className="px-6 py-4 border-b border-gray-200 bg-white flex-shrink-0">
                <div className="flex items-center justify-between mb-4">
                    <div>
                        <h2 className="text-xl font-semibold text-gray-900">Machine Management System</h2>
                        <p className="text-sm text-gray-500">Real-time monitoring and predictive analytics for all factory equipment.</p>
                    </div>
                    {activeTab === 'monitoring' && (
                        <div className="flex items-center gap-3">
                            <button onClick={() => fileInputRef.current?.click()} className="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium bg-white border border-gray-300 hover:bg-gray-100 text-gray-700 transition-colors">
                                <UploadIcon className="w-4 h-4" />
                                Import CSV
                            </button>
                            <input type="file" ref={fileInputRef} onChange={handleFileChange} accept=".csv" className="hidden" />

                            <button onClick={handleAddClick} className="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white transition-colors">
                                <PlusIcon className="w-4 h-4" />
                                Add Machine
                            </button>
                        </div>
                    )}
                </div>

                {/* Tab Navigation */}
                <div className="flex gap-2">
                    <TabButton
                        label="Real-time Monitoring"
                        icon={<ChartBarIcon className="w-4 h-4" />}
                        active={activeTab === 'monitoring'}
                        onClick={() => setActiveTab('monitoring')}
                    />
                    <TabButton
                        label="Predictive Analytics"
                        icon={<CpuChipIcon className="w-4 h-4" />}
                        active={activeTab === 'predictive'}
                        onClick={() => setActiveTab('predictive')}
                    />
                </div>
            </header>
            
            <div className="flex-1 flex overflow-hidden">
                {activeTab === 'monitoring' ? (
                    <>
                        {/* Machine List Sidebar */}
                        <aside className="w-72 bg-white border-r border-gray-200 p-4 flex flex-col overflow-y-auto">
                            <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wider px-2 mb-3">Machines ({machines.length})</h3>
                            <nav className="space-y-1">
                                {machines.map(machine => (
                                    <button
                                        key={machine.id}
                                        onClick={() => setSelectedMachineId(machine.id)}
                                        className={`w-full text-left flex items-center justify-between px-3 py-2 rounded-md text-sm transition-colors ${selectedMachineId === machine.id ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}`}
                                    >
                                        <span className="truncate">{machine.name}</span>
                                        <StatusDot status={machine.status}/>
                                    </button>
                                ))}
                            </nav>
                        </aside>

                        {/* Main Dashboard Content */}
                        <main className="flex-1 overflow-y-auto p-6">
                    {/* KPI Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <KPICard title="Active Machines" value={kpi.active} />
                        <KPICard title="Warnings" value={kpi.warnings} />
                        <KPICard title="Critical Faults" value={kpi.faults} />
                        <KPICard title="Overall Uptime" value={kpi.uptime} />
                    </div>

                    <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
                        <div className="xl:col-span-2">
                            {/* Machine Status */}
                            <div className="bg-white rounded-lg border border-gray-200 p-6 flex flex-col" style={{minHeight: '500px'}}>
                                <h3 className="font-semibold text-gray-900 mb-4">Machine Details: {selectedMachine?.name || 'N/A'}</h3>
                                {selectedMachine ? (
                                <div className="flex-1 flex flex-col">
                                    <div className="grid grid-cols-4 gap-4 text-sm mb-6">
                                        <div><p className="text-gray-500">ID</p><p className="font-mono text-gray-800">{selectedMachine.id}</p></div>
                                        <div><p className="text-gray-500">Zone</p><p className="text-gray-800">{selectedMachine.zone}</p></div>
                                        <div><p className="text-gray-500">Type</p><p className="text-gray-800">{selectedMachine.machineType}</p></div>
                                        <div><p className="text-gray-500">Protocol</p><p className="text-gray-800">{selectedMachine.protocol}</p></div>
                                    </div>
                                    <div className="flex-1 grid grid-cols-2 md:grid-cols-4 gap-6">
                                        <SignalTrendChart data={selectedMachine.trends.temperature} dataKey="value" stroke="#f87171" name="Temperature (°C)" />
                                        <SignalTrendChart data={selectedMachine.trends.vibration} dataKey="value" stroke="#fbbf24" name="Vibration (mm/s)" />
                                        <SignalTrendChart data={selectedMachine.trends.pressure} dataKey="value" stroke="#60a5fa" name="Pressure (psi)" />
                                        <SignalTrendChart data={selectedMachine.trends.humidity} dataKey="value" stroke="#818cf8" name="Humidity (%)" />
                                    </div>
                                </div>
                                ) : <div className="flex-1 flex items-center justify-center"><p className="text-center text-gray-500">Select a machine to view its details and trends.</p></div>}
                            </div>
                        </div>

                        {/* Alerts Panel */}
                        <div className="bg-white rounded-lg border border-gray-200 p-6 flex flex-col" style={{minHeight: '500px', maxHeight: '500px'}}>
                             <div className="flex justify-between items-center mb-4">
                                 <h3 className="font-semibold text-gray-900">Recent Alerts</h3>
                                 {alerts.length > alertsPerPage && (
                                     <div className="flex items-center gap-1">
                                         {/* First page */}
                                         <button
                                             onClick={() => setAlertsPage(0)}
                                             disabled={alertsPage === 0}
                                             className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                             title="First page"
                                         >
                                             <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                                             </svg>
                                         </button>

                                         {/* Previous page */}
                                         <button
                                             onClick={() => setAlertsPage(Math.max(0, alertsPage - 1))}
                                             disabled={alertsPage === 0}
                                             className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                             title="Previous page"
                                         >
                                             <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                             </svg>
                                         </button>

                                         {/* Page indicator */}
                                         <div className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-600 min-w-[70px] text-center">
                                             {alertsPage + 1} / {totalAlertsPages}
                                         </div>

                                         {/* Next page */}
                                         <button
                                             onClick={() => setAlertsPage(Math.min(totalAlertsPages - 1, alertsPage + 1))}
                                             disabled={alertsPage >= totalAlertsPages - 1}
                                             className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                             title="Next page"
                                         >
                                             <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                             </svg>
                                         </button>

                                         {/* Last page */}
                                         <button
                                             onClick={() => setAlertsPage(totalAlertsPages - 1)}
                                             disabled={alertsPage >= totalAlertsPages - 1}
                                             className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                             title="Last page"
                                         >
                                             <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                                             </svg>
                                         </button>
                                     </div>
                                 )}
                             </div>

                             {/* Alerts Content - Flexible height */}
                             <div className="flex-1 flex flex-col min-h-0">
                                <div className="flex-1 space-y-3 overflow-y-auto max-h-[350px]">
                                    {displayedAlerts.length > 0 ? displayedAlerts.map(alert => (
                                        <div key={alert.id} className="flex items-start gap-2 p-2 rounded-lg hover:bg-gray-50 transition-colors border border-gray-100">
                                            <div className={`mt-0.5 flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${alert.type === 'Failure' ? 'bg-red-100' : 'bg-yellow-100'}`}>
                                                <ExclamationTriangleIcon className={`w-3 h-3 ${alert.type === 'Failure' ? 'text-red-500' : 'text-yellow-500'}`}/>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between mb-1">
                                                    <p className="font-medium text-gray-800 text-xs truncate">{alert.machineName}</p>
                                                    <span className={`px-1.5 py-0.5 text-xs rounded-full ${alert.type === 'Failure' ? 'bg-red-100 text-red-700' : 'bg-yellow-100 text-yellow-700'}`}>
                                                        {alert.type}
                                                    </span>
                                                </div>
                                                <p className="text-xs text-gray-600 line-clamp-2 mb-1">{alert.message}</p>
                                                <p className="text-xs text-gray-400">{new Date(alert.timestamp).toLocaleString()}</p>
                                            </div>
                                        </div>
                                    )) : (
                                        <div className="flex-1 flex items-center justify-center">
                                            <div className="text-center">
                                                <div className="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
                                                    <ExclamationTriangleIcon className="w-6 h-6 text-gray-400" />
                                                </div>
                                                <p className="text-sm text-gray-500">No recent alerts</p>
                                                <p className="text-xs text-gray-400 mt-1">All systems operating normally</p>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {/* Alert Summary Footer */}
                                {alerts.length > 0 && (
                                    <div className="mt-4 pt-3 border-t border-gray-100">
                                        <div className="flex items-center justify-between text-xs text-gray-500">
                                            <span>Total: {alerts.length} alerts</span>
                                            <span>Page {alertsPage + 1} of {totalAlertsPages}</span>
                                        </div>
                                    </div>
                                )}
                             </div>
                        </div>
                    </div>

                     {/* Full Machine Table */}
                    <div className="mt-6 bg-white rounded-lg border border-gray-200">
                         <h3 className="font-semibold text-gray-900 p-6">All Machines Overview</h3>
                         <div className="overflow-x-auto">
                             <table className="w-full text-sm text-left text-gray-600">
                                <thead className="text-xs text-gray-500 uppercase bg-gray-50">
                                    <tr>
                                        <th className="py-3 px-6">Status</th>
                                        <th className="py-3 px-6">Machine</th>
                                        <th className="py-3 px-6">Type</th>
                                        <th className="py-3 px-6">Health</th>
                                        <th className="py-3 px-6">Temp (°C)</th>
                                        <th className="py-3 px-6">Vib (mm/s)</th>
                                        <th className="py-3 px-6">Pressure (psi)</th>
                                        <th className="py-3 px-6">Humidity (%)</th>
                                        <th className="py-3 px-6 text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200">
                                    {machines.map(m => (
                                        <tr key={m.id} className="hover:bg-gray-50">
                                            <td className="py-4 px-6"><div className="flex items-center gap-2"><StatusDot status={m.status}/> <span>{m.status}</span></div></td>
                                            <td className="py-4 px-6 font-medium text-gray-900">{m.name}</td>
                                            <td className="py-4 px-6">{m.machineType}</td>
                                            <td className={`py-4 px-6 font-semibold ${m.healthScore > 90 ? 'text-green-600' : m.healthScore > 70 ? 'text-yellow-500' : 'text-red-600'}`}>{m.healthScore}%</td>
                                            <td className="py-4 px-6">{m.temp.toFixed(1)}</td>
                                            <td className="py-4 px-6">{m.vibration.toFixed(1)}</td>
                                            <td className="py-4 px-6">{m.pressure.toFixed(1)}</td>
                                            <td className="py-4 px-6">{m.humidity.toFixed(1)}</td>
                                            <td className="py-4 px-6">
                                                <div className="flex items-center justify-center gap-2">
                                                    <button onClick={() => handleEditClick(m)} className="p-1.5 text-gray-500 hover:text-blue-600" aria-label={`Edit ${m.name}`}><PencilIcon className="w-4 h-4" /></button>
                                                    <button onClick={() => handleDeleteClick(m)} className="p-1.5 text-gray-500 hover:text-red-600" aria-label={`Delete ${m.name}`}><XCircleIcon className="w-4 h-4" /></button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                             </table>
                         </div>
                    </div>

                        </main>
                    </>
                ) : (
                    /* Predictive Analytics Panel */
                    <div className="flex-1 overflow-y-auto">
                        <PredictiveAnalysisPanel />
                    </div>
                )}
            </div>
            
            {isModalOpen && <MachineFormModal machine={editingMachine} onSave={handleSaveMachine} onClose={() => setIsModalOpen(false)} />}
            {isDeleteConfirmOpen && <DeleteConfirmationModal onConfirm={confirmDelete} onCancel={() => setIsDeleteConfirmOpen(false)} machineName={machineToDelete?.name || ''} />}
        </div>
    );
};


// --- Modal Components ---

interface MachineFormModalProps {
    machine: Machine | null;
    onSave: (data: Partial<Machine>) => void;
    onClose: () => void;
}
const MachineFormModal: React.FC<MachineFormModalProps> = ({ machine, onSave, onClose }) => {
    const [formData, setFormData] = useState({
        name: machine?.name || '',
        zone: machine?.zone || 'Zone A',
        machineType: machine?.machineType || 'Air Compressor',
        protocol: machine?.protocol || 'MQTT',
    });

    const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSave(formData);
    };

    return (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50" role="dialog" aria-modal="true">
            <div className="absolute inset-0" onClick={onClose}></div>
            <form onSubmit={handleSubmit} className="relative bg-white rounded-lg shadow-xl p-8 max-w-lg w-full border border-gray-200 space-y-4">
                <h3 className="text-lg font-bold text-gray-900">{machine ? 'Edit Machine' : 'Add New Machine'}</h3>
                <div>
                    <label htmlFor="name" className="text-sm font-medium text-gray-600 mb-1 block">Machine Name</label>
                    <input id="name" type="text" name="name" value={formData.name} onChange={handleChange} required className="w-full bg-gray-100 border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-gray-900"/>
                </div>
                 <div>
                    <label htmlFor="machineType" className="text-sm font-medium text-gray-600 mb-1 block">Machine Type</label>
                    <input id="machineType" type="text" name="machineType" value={formData.machineType} onChange={handleChange} required className="w-full bg-gray-100 border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-gray-900"/>
                </div>
                 <div className="grid grid-cols-2 gap-4">
                    <div>
                        <label htmlFor="zone" className="text-sm font-medium text-gray-600 mb-1 block">Zone</label>
                        <select id="zone" name="zone" value={formData.zone} onChange={handleChange} className="w-full bg-gray-100 border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-gray-900">
                            <option>Zone A</option>
                            <option>Zone B</option>
                            <option>Zone C</option>
                        </select>
                    </div>
                     <div>
                        <label htmlFor="protocol" className="text-sm font-medium text-gray-600 mb-1 block">Protocol</label>
                        <select id="protocol" name="protocol" value={formData.protocol} onChange={handleChange} className="w-full bg-gray-100 border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-gray-900">
                            <option>MQTT</option>
                            <option>OPC-UA</option>
                            <option>Modbus</option>
                        </select>
                    </div>
                </div>
                <div className="flex justify-end gap-4 pt-4">
                    <button type="button" onClick={onClose} className="px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg">Cancel</button>
                    <button type="submit" className="px-4 py-2 text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 rounded-lg">Save</button>
                </div>
            </form>
        </div>
    );
};

interface DeleteConfirmationModalProps {
    machineName: string;
    onConfirm: () => void;
    onCancel: () => void;
}
const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({ machineName, onConfirm, onCancel }) => (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50" role="dialog" aria-modal="true">
        <div className="absolute inset-0" onClick={onCancel}></div>
        <div className="relative bg-white rounded-lg shadow-xl p-8 max-w-md w-full border">
            <h3 className="text-lg font-bold text-gray-900">Confirm Deletion</h3>
            <p className="text-gray-600 my-4">Are you sure you want to delete the machine "{machineName}"? This action cannot be undone.</p>
            <div className="flex justify-end gap-4">
                <button onClick={onCancel} className="px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg">Cancel</button>
                <button onClick={onConfirm} className="px-4 py-2 text-sm font-semibold text-white bg-red-600 hover:bg-red-700 rounded-lg">Delete</button>
            </div>
        </div>
    </div>
);


export default MaintenanceDashboard;