
import React, { createContext, useState, ReactNode, useEffect, useMemo } from 'react';
import type { Ticket, User, FormTemplate, Machine, Alert, ProductManual, EmployeeDirectoryEntry, KnowledgeArticle, Announcement, Policy, QuickLink, AppView, AIAssistantKnowledge } from '../types';
import { USERS, INITIAL_TICKETS, INITIAL_FORM_TEMPLATES, ORGANIZATION, MAINTENANCE_DATA, WIKI_PRODUCT_MANUALS, WIKI_EMPLOYEE_DIRECTORY, WIKI_KNOWLEDGE_ARTICLES, WIKI_ANNOUNCEMENTS, WIKI_POLICIES, WIKI_QUICK_LINKS, AI_ASSISTANT_KNOWLEDGE_BASE } from '../constants';
import { getCombinedKnowledgeBase } from '../services/aiAssistantService';

interface AppContextState {
    tickets: Ticket[];
    users: User[];
    organization: typeof ORGANIZATION;
    currentUser: User;
    formTemplates: FormTemplate[];
    
    // Wiki Data
    wikiProductManuals: ProductManual[];
    wikiEmployeeDirectory: EmployeeDirectoryEntry[];
    wikiKnowledgeArticles: KnowledgeArticle[];
    wikiAnnouncements: Announcement[];
    wikiPolicies: Policy[];
    wikiQuickLinks: QuickLink[];
    
    // AI Assistant Data
    aiKnowledgeBase: AIAssistantKnowledge[];

    addTicket: (ticket: Ticket) => void;
    updateTicket: (updatedTicket: Ticket) => void;
    addFormTemplate: (template: FormTemplate) => void;
    updateFormTemplate: (template: FormTemplate) => void;
    deleteFormTemplate: (templateId: string) => void;
    setActiveView: (view: AppView) => void;

    // Wiki CRUD operations
    addKnowledgeArticle: (article: KnowledgeArticle) => void;
    updateKnowledgeArticle: (article: KnowledgeArticle) => void;
    deleteKnowledgeArticle: (articleId: string) => void;
    addAnnouncement: (announcement: Announcement) => void;
    updateAnnouncement: (announcement: Announcement) => void;
    deleteAnnouncement: (announcementId: string) => void;
    addPolicy: (policy: Policy) => void;
    updatePolicy: (policy: Policy) => void;
    deletePolicy: (policyId: string) => void;
    
    // Machine Management
    machines: Machine[];
    alerts: Alert[];
    addMachine: (machineData: Partial<Omit<Machine, 'id' | 'status' | 'faultCode' | 'healthScore' | 'trends'>>) => void;
    updateMachine: (machine: Partial<Machine> & {id: string}) => void;
    deleteMachine: (machineId: string) => void;
    addMachinesFromCSV: (csvData: string) => void;
}

export const AppContext = createContext<AppContextState>({} as AppContextState);

interface AppProviderProps {
    children: ReactNode;
    selectedUser: User;
    setActiveView: (view: AppView) => void;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children, selectedUser, setActiveView }) => {
    const [tickets, setTickets] = useState<Ticket[]>(INITIAL_TICKETS);
    const [formTemplates, setFormTemplates] = useState<FormTemplate[]>(INITIAL_FORM_TEMPLATES);
    const [machines, setMachines] = useState<Machine[]>(MAINTENANCE_DATA.machines);
    const [alerts, setAlerts] = useState<Alert[]>(MAINTENANCE_DATA.alerts);

    // Wiki content state
    const [knowledgeArticles, setKnowledgeArticles] = useState<KnowledgeArticle[]>(WIKI_KNOWLEDGE_ARTICLES);
    const [announcements, setAnnouncements] = useState<Announcement[]>(WIKI_ANNOUNCEMENTS);
    const [policies, setPolicies] = useState<Policy[]>(WIKI_POLICIES);

    const addTicket = (ticket: Ticket) => {
        setTickets(prev => [ticket, ...prev].sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));
    };

    const updateTicket = (updatedTicket: Ticket) => {
        setTickets(prev => prev.map(t => t.id === updatedTicket.id ? updatedTicket : t));
    };
    
    const addFormTemplate = (template: FormTemplate) => {
        setFormTemplates(prev => [...prev, template]);
    };

    const updateFormTemplate = (template: FormTemplate) => {
        setFormTemplates(prev => prev.map(t => t.id === template.id ? template : t));
    };

    const deleteFormTemplate = (templateId: string) => {
        setFormTemplates(prev => prev.filter(t => t.id !== templateId));
    };

    // Wiki CRUD functions
    const addKnowledgeArticle = (article: KnowledgeArticle) => {
        setKnowledgeArticles(prev => [...prev, article]);
    };

    const updateKnowledgeArticle = (article: KnowledgeArticle) => {
        setKnowledgeArticles(prev => prev.map(a => a.id === article.id ? article : a));
    };

    const deleteKnowledgeArticle = (articleId: string) => {
        setKnowledgeArticles(prev => prev.filter(a => a.id !== articleId));
    };

    const addAnnouncement = (announcement: Announcement) => {
        setAnnouncements(prev => [...prev, announcement]);
    };

    const updateAnnouncement = (announcement: Announcement) => {
        setAnnouncements(prev => prev.map(a => a.id === announcement.id ? announcement : a));
    };

    const deleteAnnouncement = (announcementId: string) => {
        setAnnouncements(prev => prev.filter(a => a.id !== announcementId));
    };

    const addPolicy = (policy: Policy) => {
        setPolicies(prev => [...prev, policy]);
    };

    const updatePolicy = (policy: Policy) => {
        setPolicies(prev => prev.map(p => p.id === policy.id ? policy : p));
    };

    const deletePolicy = (policyId: string) => {
        setPolicies(prev => prev.filter(p => p.id !== policyId));
    };

    // --- Machine Management ---
    const addMachine = (machineData: Partial<Omit<Machine, 'id' | 'status' | 'faultCode' | 'healthScore' | 'trends'>>) => {
        const newMachine: Machine = {
            id: `M-${Date.now().toString().slice(-4)}`,
            name: machineData.name || 'New Machine',
            zone: machineData.zone || 'Zone A',
            machineType: machineData.machineType || 'Generic Machine',
            protocol: machineData.protocol || 'MQTT',
            status: 'Operational',
            faultCode: null,
            healthScore: 100,
            temp: 50,
            vibration: 10,
            pressure: 100,
            humidity: 50,
            trends: {
                temperature: [],
                vibration: [],
                pressure: [],
                humidity: [],
            }
        };
        setMachines(prev => [...prev, newMachine]);
    };
    
    const updateMachine = (updatedMachineData: Partial<Machine> & { id: string }) => {
        setMachines(prev => prev.map(m => m.id === updatedMachineData.id ? { ...m, ...updatedMachineData } : m));
    };

    const deleteMachine = (machineId: string) => {
        setMachines(prev => prev.filter(m => m.id !== machineId));
    };
    
    const addMachinesFromCSV = (csvData: string) => {
        const newMachines: Machine[] = [];
        const rows = csvData.split('\n').slice(1); // Assuming header row
        rows.forEach(row => {
            const [id, name, zone, machineType, protocol] = row.split(',').map(item => item.trim());
            if(id && name && zone && machineType && protocol) {
                newMachines.push({
                    id, name, zone, machineType, protocol: protocol as Machine['protocol'],
                    status: 'Operational', faultCode: null, healthScore: 100,
                    temp: 50, vibration: 10, pressure: 100, humidity: 50,
                    trends: { temperature: [], vibration: [], pressure: [], humidity: [] }
                });
            }
        });
        setMachines(prev => [...prev, ...newMachines]);
    };
    
    // Real-time data simulation
    useEffect(() => {
        const interval = setInterval(() => {
            let newAlerts: Alert[] = [];
            setMachines(prevMachines => prevMachines.map(m => {
                const newMachine = { ...m };
                // Simulate data fluctuation
                newMachine.temp = parseFloat((m.temp + (Math.random() - 0.5) * 2).toFixed(1));
                newMachine.vibration = parseFloat((m.vibration + (Math.random() - 0.5) * 0.5).toFixed(1));
                newMachine.pressure = parseFloat((m.pressure + (Math.random() - 0.5) * 5).toFixed(1));
                newMachine.humidity = parseFloat((m.humidity + (Math.random() - 0.5) * 1).toFixed(1));
                
                // Keep trends to a reasonable size
                const now = new Date().toISOString();
                newMachine.trends = {
                    temperature: [...m.trends.temperature, { timestamp: now, value: newMachine.temp }].slice(-30),
                    vibration: [...m.trends.vibration, { timestamp: now, value: newMachine.vibration }].slice(-30),
                    pressure: [...m.trends.pressure, { timestamp: now, value: newMachine.pressure }].slice(-30),
                    humidity: [...m.trends.humidity, { timestamp: now, value: newMachine.humidity }].slice(-30),
                };

                // Update status and create alerts
                let healthScore = 100;
                let status: Machine['status'] = 'Operational';
                let faultCode: string | null = null;
                
                if (newMachine.temp > 85 || newMachine.vibration > 14.5) {
                    status = 'Failure';
                    healthScore -= 50;
                    faultCode = 'F-CRIT';
                    newAlerts.push({id: `A-${Date.now()}${m.id}`, machineName: m.name, message: `Critical threshold exceeded (Temp: ${newMachine.temp}, Vib: ${newMachine.vibration})`, timestamp: now, type: 'Failure'});
                } else if (newMachine.temp > 60 || newMachine.vibration > 11) {
                    status = 'Warning';
                    healthScore -= 25;
                    faultCode = 'F-WARN';
                     newAlerts.push({id: `A-${Date.now()}${m.id}`, machineName: m.name, message: `Operating outside normal parameters.`, timestamp: now, type: 'Warning'});
                }
                
                newMachine.status = status;
                newMachine.healthScore = healthScore > 0 ? healthScore : 0;
                newMachine.faultCode = faultCode;

                return newMachine;
            }));

            if(newAlerts.length > 0) {
                 setAlerts(prevAlerts => {
                    const existingAlertMessagesForMachine = new Set(prevAlerts.slice(0, 5).map(a => a.machineName + a.type));
                    const uniqueNewAlerts = newAlerts.filter(a => !existingAlertMessagesForMachine.has(a.machineName + a.type));
                    return [...uniqueNewAlerts, ...prevAlerts].slice(0, 10);
                 });
            }

        }, 3000); // Update every 3 seconds
        
        return () => clearInterval(interval);
    }, []);

    const value: AppContextState = {
        tickets,
        users: USERS,
        organization: ORGANIZATION,
        currentUser: selectedUser,
        formTemplates,
        
        wikiProductManuals: WIKI_PRODUCT_MANUALS,
        wikiEmployeeDirectory: WIKI_EMPLOYEE_DIRECTORY,
        wikiKnowledgeArticles: knowledgeArticles,
        wikiAnnouncements: announcements,
        wikiPolicies: policies,
        wikiQuickLinks: WIKI_QUICK_LINKS,

        aiKnowledgeBase: getCombinedKnowledgeBase(),

        addTicket,
        updateTicket,
        addFormTemplate,
        updateFormTemplate,
        deleteFormTemplate,
        setActiveView,

        // Wiki CRUD operations
        addKnowledgeArticle,
        updateKnowledgeArticle,
        deleteKnowledgeArticle,
        addAnnouncement,
        updateAnnouncement,
        deleteAnnouncement,
        addPolicy,
        updatePolicy,
        deletePolicy,
        // machine management
        machines,
        alerts,
        addMachine,
        updateMachine,
        deleteMachine,
        addMachinesFromCSV,
    };

    return (
        <AppContext.Provider value={value}>
            {children}
        </AppContext.Provider>
    );
};