



import React, { useState, useContext } from 'react';
import { BuildingOfficeIcon, UserIcon, OrganizationIcon } from './icons/Icons';
import { AppContext } from './AppContext';
import type { User, OrganizationNode, Ticket, View } from '../types';

const EmployeeDetailPanel: React.FC<{ user: User, onSelectTicket: (id: string) => void }> = ({ user, onSelectTicket }) => {
    const { tickets } = useContext(AppContext);
    const assignedTickets = tickets.filter(t => t.assignees.includes(user.id));
    
    return (
        <>
            <div className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center text-5xl font-bold text-gray-600 mb-4 border-4 border-gray-100">{user.avatar}</div>
            <h3 className="font-semibold text-lg text-gray-900">{user.name}</h3>
            <p className="text-gray-500">{user.role} - {user.department}</p>
            <div className="text-sm text-gray-500 mt-1 flex items-center gap-2">
                <span className={`w-2.5 h-2.5 rounded-full ${user.status === 'Online' ? 'bg-green-500' : user.status === 'On Leave' ? 'bg-gray-400' : 'bg-yellow-400'}`}></span>
                {user.status}
            </div>
            <div className="w-full bg-gray-100 rounded-lg p-3 mt-6 text-left">
                <p className="text-sm font-semibold text-gray-700">Current Workload</p>
                <p className="text-xs text-gray-500">{user.workload.urgent} Urgent, {user.workload.normal} Normal</p>
            </div>

            {/* Experience Section */}
            {user.experience && Object.keys(user.experience).length > 0 && (
                <div className="w-full bg-blue-50 rounded-lg p-3 mt-4 text-left">
                    <p className="text-sm font-semibold text-gray-700 mb-2">Experience & Success Rates</p>
                    <div className="space-y-2">
                        {Object.entries(user.experience).map(([category, score]) => {
                            const categoryName = {
                                itSupport: 'IT Support',
                                finance: 'Finance',
                                adminRequest: 'Admin Request',
                                marketing: 'Marketing',
                                humanResources: 'Human Resources',
                                qualityAssurance: 'Quality Assurance',
                                operations: 'Operations',
                                rnd: 'R&D'
                            }[category] || category;

                            const scoreColor = score >= 90 ? 'text-green-600' : score >= 80 ? 'text-blue-600' : 'text-yellow-600';

                            return (
                                <div key={category} className="flex justify-between items-center">
                                    <span className="text-xs text-gray-600">{categoryName}</span>
                                    <span className={`text-xs font-semibold ${scoreColor}`}>{score}%</span>
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}
             <div className="w-full mt-4 text-left">
                <h4 className="font-semibold text-gray-800 mb-2">Assigned Tickets ({assignedTickets.length})</h4>
                <div className="space-y-2 max-h-64 overflow-y-auto pr-2">
                    {assignedTickets.length > 0 ? assignedTickets.map(ticket => (
                        <button key={ticket.id} onClick={() => onSelectTicket(ticket.id)} className="w-full text-left bg-gray-100 p-2 rounded-md hover:bg-gray-200">
                           <p className="text-sm font-medium text-gray-800 truncate">{ticket.title}</p>
                           <p className="text-xs text-gray-500">{ticket.id} - {ticket.status}</p>
                        </button>
                    )) : <p className="text-xs text-gray-500">No tickets assigned.</p>}
                </div>
            </div>
        </>
    );
};


const EmployeeCard: React.FC<{ user: User; isManager?: boolean; onClick: () => void, isSelected: boolean }> = ({ user, isManager, onClick, isSelected }) => {
    const statusColor = user.status === 'Online' ? 'bg-green-500' : user.status === 'On Leave' ? 'bg-gray-400' : 'bg-yellow-400';
    
    return (
        <button onClick={onClick} className={`bg-white border border-gray-200 rounded-lg p-4 text-left w-64 flex-shrink-0 transition-all duration-200 ${isSelected ? 'border-blue-500 ring-2 ring-blue-500/20 transform scale-105' : 'hover:border-blue-400 hover:bg-blue-50/50'}`}>
            {isManager && <p className="text-sm font-bold text-gray-900 mb-2">{user.department}</p>}
            <div className="flex justify-between items-start">
                <div>
                    <p className="font-semibold text-gray-800">{user.name}</p>
                    <p className="text-xs text-gray-500">{user.role}</p>
                </div>
                <div className="flex items-center gap-1.5 text-xs text-gray-500">
                    <span className={`w-2 h-2 rounded-full ${statusColor}`}></span>
                    {user.status}
                </div>
            </div>
            <div className="text-xs text-gray-500 mt-3 pt-3 border-t border-gray-200">
                Workload: {user.workload.urgent} Urgent, {user.workload.normal} Normal
            </div>
        </button>
    );
};

const DepartmentNode: React.FC<{ node: OrganizationNode, selectedEmployee: User | null, onSelectEmployee: (user: User) => void }> = ({ node, selectedEmployee, onSelectEmployee }) => {
    const { users } = useContext(AppContext);
    const user = users.find(u => u.id === node.id);
    if (!user) return null;

    return (
        <div className="relative flex flex-col items-center pt-8 px-4">
            {/* Vertical line connecting to parent */}
            <div className="absolute top-0 h-8 w-px bg-gray-300"></div>

            <EmployeeCard 
                user={user} 
                isManager={user.role === 'Manager' || user.role === 'CEO'}
                onClick={() => onSelectEmployee(user)} 
                isSelected={selectedEmployee?.id === user.id}
            />
            {node.children && node.children.length > 0 && (
                <div className="flex flex-row items-start relative mt-8">
                    {/* Horizontal line */}
                    <div className="absolute -top-4 left-0 h-px bg-gray-300 w-full"></div>
                    {/* Vertical line from card to horizontal line */}
                    <div className="absolute -top-4 h-4 w-px bg-gray-300"></div>
                     {node.children.map(childNode => (
                        <DepartmentNode key={childNode.id} node={childNode} selectedEmployee={selectedEmployee} onSelectEmployee={onSelectEmployee} />
                    ))}
                </div>
            )}
        </div>
    );
};

const OrganizationChart: React.FC<{ setActiveView: (view: View) => void }> = ({ setActiveView }) => {
    const { organization } = useContext(AppContext);
    const [selectedEmployee, setSelectedEmployee] = useState<User | null>(null);

    const handleSelectTicket = (id: string) => {
        setActiveView(`detail-${id}`);
    };

    return (
        <div className="flex-1 flex overflow-hidden">
            <div className="flex-1 overflow-auto p-8 bg-white">
                 <div className="inline-block min-w-full">
                    <DepartmentNode node={organization} selectedEmployee={selectedEmployee} onSelectEmployee={setSelectedEmployee}/>
                 </div>
            </div>
            <aside className="w-96 border-l border-gray-200 p-8 flex-shrink-0 flex flex-col items-center justify-start text-center bg-gray-50/50 overflow-y-auto">
                {selectedEmployee ? (
                    <EmployeeDetailPanel user={selectedEmployee} onSelectTicket={handleSelectTicket}/>
                ) : (
                    <div className="flex flex-col items-center justify-center h-full text-gray-400">
                        <OrganizationIcon className="w-16 h-16 mb-4"/>
                        <h3 className="font-bold text-lg text-gray-500">Select an Employee</h3>
                        <p className="text-sm">Click on an employee card in the chart to view their details and assigned tickets.</p>
                    </div>
                )}
            </aside>
        </div>
    );
};

export default OrganizationChart;