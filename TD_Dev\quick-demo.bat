@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🎯 Daikin AI System - 5-Minute Quick Demo
echo ========================================
echo.

:: Set color
color 0E

:: Switch to project root directory
cd /d "%~dp0\.."

echo 💡 This is a 5-minute quick demo script
echo It will guide you through the core system features
echo.

:: Check system status
curl -s http://localhost:5173 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ System not running, starting system...
    echo Please wait, system starting...
    start /B npm run dev
    timeout /t 10 /nobreak >nul
    
    :: Check again
    curl -s http://localhost:5173 >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ System startup failed, please manually run: npm run dev
        pause
        exit /b 1
    )
)

echo ✅ System is running
echo.

echo 🚀 Starting 5-minute quick demo...
echo.

:: Demo Step 1
echo ========================================
echo 📱 Step 1: Open System Homepage (30 seconds)
echo ========================================
echo.
echo Opening browser...
start http://localhost:5173
echo.
echo 👀 Please observe:
echo   • System main interface loading
echo   • Navigation menu display
echo   • Various function module tabs
echo.
echo Press any key to continue to next step...
pause >nul

:: Demo Step 2
echo.
echo ========================================
echo 🤖 Step 2: AI Assistant Demo (90 seconds)
echo ========================================
echo.
echo 📋 Please perform the following operations in browser:
echo.
echo 1. Click "AI Assistant" tab
echo 2. Enter in input box: "What is R-32 refrigerant?"
echo 3. Click send or press Enter
echo 4. Observe AI response and source citations
echo.
echo 👀 Please observe:
echo   • AI response speed (should be within 5 seconds)
echo   • Response content accuracy
echo   • Source document citations display
echo   • Confidence score ratings
echo.
echo Press any key to continue to next step...
pause >nul

:: Demo Step 3
echo.
echo ========================================
echo 📋 Step 3: Smart Forms Demo (90 seconds)
echo ========================================
echo.
echo 📋 Please perform the following operations in browser:
echo.
echo 1. Click "Smart Forms" tab
echo 2. Click "Create New Ticket"
echo 3. Select "Equipment Purchase" category
echo 4. Enter: "New injection molding machine purchase request"
echo 5. Observe auto-fill functionality
echo.
echo 👀 Please observe:
echo   • Form field auto-filling
echo   • Smart suggestions display
echo   • Budget validation functionality
echo   • Vendor recommendations
echo.
echo Press any key to continue to next step...
pause >nul

:: Demo Step 4
echo.
echo ========================================
echo 🔧 Step 4: Maintenance Dashboard Demo (90 seconds)
echo ========================================
echo.
echo 📋 Please perform the following operations in browser:
echo.
echo 1. Click "Maintenance Dashboard" tab
echo 2. View equipment health status
echo 3. Click any equipment to view details
echo 4. Observe sensor data and alerts
echo.
echo 👀 Please observe:
echo   • Equipment status visualization
echo   • Health score display
echo   • Alerts and notifications
echo   • Real-time data updates
echo.
echo Press any key to continue to next step...
pause >nul

:: Demo Step 5
echo.
echo ========================================
echo 📊 Step 5: Predictive Analytics Demo (90 seconds)
echo ========================================
echo.
echo 📋 Please perform the following operations in browser:
echo.
echo 1. In Maintenance Dashboard, click "Predictive Analytics" tab
echo 2. Select any equipment
echo 3. Click "Analyze" button
echo 4. View cost comparison analysis
echo.
echo 👀 Please observe:
echo   • Prediction results display
echo   • Cost comparison analysis
echo   • ROI calculations
echo   • Savings amount display
echo.
echo Press any key to view demo summary...
pause >nul

:: Demo Summary
echo.
echo ========================================
echo 🎉 Demo Complete - Core Value Summary
echo ========================================
echo.
echo 💰 Financial Value Proof:
echo   • System Investment: RM240,000
echo   • Annual Savings: RM450,000+
echo   • Return on Investment: 187.5%%
echo.
echo ⚡ Efficiency Improvements:
echo   • Emergency Response: 15 minutes vs 72 hours
echo   • Approval Process: 3 days vs 21 days
echo   • Prediction Accuracy: 94.7%%
echo.
echo 🎯 Core Function Verification:
echo   ✅ AI Assistant - Smart Q&A and knowledge retrieval
echo   ✅ Smart Forms - Auto-fill and process optimization
echo   ✅ Maintenance Dashboard - Real-time monitoring and alerts
echo   ✅ Predictive Analytics - Cost savings and ROI calculation
echo.
echo 📈 Business Impact:
echo   • Reduce equipment downtime by 85%%
echo   • Improve approval efficiency by 85%%
echo   • Lower maintenance costs by 70%%
echo   • Enhance decision accuracy by 94%%
echo.

:: Ask if detailed testing should be run
echo.
set /p detailed_test="Run detailed testing verification? (y/n): "
if /i "%detailed_test%"=="y" (
    echo.
    echo 🧪 Starting detailed testing...
    call run-tests.bat
) else (
    echo.
    echo 📚 For detailed testing, please run:
    echo   • run-tests.bat (automated testing)
    echo   • View TESTING_GUIDE.md (complete test guide)
)

echo.
echo ========================================
echo 🎯 Demo script execution completed!
echo ========================================
echo.
echo 📋 Next Steps Recommendations:
echo   1. Run complete test suite to verify all functions
echo   2. View detailed documentation for advanced features
echo   3. Customize demo scenarios based on actual needs
echo.
echo 📄 Related Documentation:
echo   • TESTING_GUIDE.md - Complete testing guide
echo   • COMPLETE_TEST_GUIDE.md - Demo scenario details
echo   • SETUP_GUIDE.md - System configuration guide
echo.

pause
