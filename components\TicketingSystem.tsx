





import React from 'react';
import type { View } from '../types';
import { DashboardIcon, KanbanIcon, TicketIcon, OrganizationIcon } from './icons/Icons';
import Dashboard from './Dashboard';
import KanbanBoard from './WorkflowKanban';
import AllTicketsView from './AllTicketsView';
import TicketDetailView from './TicketDetailView';
import OrganizationChart from './OrganizationChart';

type IconElement = React.ReactElement<React.SVGProps<SVGSVGElement>>;

interface TicketingSystemProps {
    activeView: View;
    onActiveViewChange: (view: View) => void;
}

const TicketingSystem: React.FC<TicketingSystemProps> = ({ activeView, onActiveViewChange }) => {

    const handleSelectTicket = (id: string) => {
        onActiveViewChange(`detail-${id}`);
    }

    const renderContent = () => {
        if (activeView.startsWith('detail-')) {
            const ticketId = activeView.split('detail-')[1];
            return <TicketDetailView ticketId={ticketId} onBack={() => onActiveViewChange('all-tickets')} />;
        }

        switch(activeView) {
            case 'dashboard': return <Dashboard onSelectTicket={handleSelectTicket} />;
            case 'kanban': return <KanbanBoard setActiveView={onActiveViewChange} />;
            case 'all-tickets': return <AllTicketsView onSelectTicket={handleSelectTicket} />;
            case 'organization': return <OrganizationChart setActiveView={onActiveViewChange}/>;
            default: return <Dashboard onSelectTicket={handleSelectTicket} />;
        }
    };
    
    // Determine the active state for the navigation. If we're on a detail page, 'All Tickets' should be active.
    const activeNav = activeView.startsWith('detail-') ? 'all-tickets' : activeView;

    return (
         <div className="flex-1 flex flex-col overflow-hidden">
            <header className="px-6 py-3 bg-white border-b border-gray-200 flex items-center gap-4 flex-shrink-0">
                 <SubNavItem icon={<DashboardIcon />} label="Dashboard" active={activeNav === 'dashboard'} onClick={() => onActiveViewChange('dashboard')} />
                 <SubNavItem icon={<KanbanIcon />} label="Kanban Board" active={activeNav === 'kanban'} onClick={() => onActiveViewChange('kanban')} />
                 <SubNavItem icon={<TicketIcon />} label="All Tickets" active={activeNav === 'all-tickets'} onClick={() => onActiveViewChange('all-tickets')} />
                 <SubNavItem icon={<OrganizationIcon />} label="Organization" active={activeNav === 'organization'} onClick={() => onActiveViewChange('organization')} />
            </header>
            <div className="flex-1 flex flex-col overflow-hidden">
                {renderContent()}
            </div>
        </div>
    );
};

interface SubNavItemProps {
    icon: IconElement,
    label: string,
    active: boolean,
    onClick: () => void
}

const SubNavItem: React.FC<SubNavItemProps> = ({ icon, label, active, onClick}) => (
    <button onClick={onClick} className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${active ? 'bg-gray-200 text-gray-800' : 'text-gray-500 hover:bg-gray-200 hover:text-gray-800'}`}>
        {React.cloneElement(icon, { className: 'w-4 h-4' })}
        {label}
    </button>
);

export default TicketingSystem;