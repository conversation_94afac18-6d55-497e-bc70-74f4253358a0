
import React, { use<PERSON>emo, useContext } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Legend } from 'recharts';
import type { Ticket, User, TicketStatus } from '../types';
import { STATUS_COLORS, TICKET_STATUSES } from '../constants';
import { AppContext } from './AppContext';
import { TicketIcon, ChartPieIcon, ExclamationTriangleIcon, UserCircleIcon, Bars3Icon, FireIcon } from './icons/Icons';

const KPICard: React.FC<{ title: string; value: number | string; icon: React.ReactNode }> = ({ title, value, icon }) => (
    <div className="bg-white p-4 rounded-lg border border-gray-200 flex items-center gap-4">
        <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
            {icon}
        </div>
        <div>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            <p className="text-sm text-gray-500">{title}</p>
        </div>
    </div>
);

const TicketListItem: React.FC<{ticket: Ticket; onClick: () => void}> = ({ticket, onClick}) => (
    <button onClick={onClick} className="w-full text-left flex justify-between items-center py-2.5 px-2 hover:bg-gray-100 rounded-md">
        <div>
            <p className="font-medium text-gray-800 text-sm truncate">{ticket.title}</p>
            <p className="text-xs text-gray-500">{ticket.id}</p>
        </div>
        <span className={`text-xs font-semibold px-2 py-0.5 rounded ${
            ticket.status === 'Received' ? 'bg-blue-100 text-blue-800' :
            ticket.status === 'In Progress' ? 'bg-violet-100 text-violet-800' :
            'bg-green-100 text-green-800'
        }`}>{ticket.status}</span>
    </button>
);

const RecentActivityItem: React.FC<{ activity: {id: string, text: string, timestamp: string}, onClick: () => void }> = ({ activity, onClick }) => (
    <button onClick={onClick} className="w-full text-left text-sm hover:bg-gray-100 rounded-md p-2">
        <p className="text-gray-700">{activity.text}</p>
        <p className="text-xs text-gray-500">{new Date(activity.timestamp).toLocaleString()}</p>
    </button>
);


const Dashboard: React.FC<{ onSelectTicket: (id: string) => void }> = ({ onSelectTicket }) => {
    const { tickets, currentUser } = useContext(AppContext);
    
    const kpiData = useMemo(() => ({
        total: tickets.length,
        inProgress: tickets.filter(t => t.status === 'In Progress').length,
        highUrgency: tickets.filter(t => t.urgency === 'High').length,
        unassigned: tickets.filter(t => t.assignees.length === 0).length,
    }), [tickets]);

    const statusChartData = useMemo(() => {
        const counts = tickets.reduce((acc, ticket) => {
            acc[ticket.status] = (acc[ticket.status] || 0) + 1;
            return acc;
        }, {} as Record<TicketStatus, number>);

        return TICKET_STATUSES.map(status => ({
            name: status,
            value: counts[status] || 0,
        })).filter(item => item.value > 0);
    }, [tickets]);

    const highUrgencyTickets = useMemo(() =>
        tickets.filter(t => t.urgency === 'High' && (t.status === 'Received' || t.status === 'In Progress' || t.status === 'Reopened'))
        .sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5)
    , [tickets]);

    const myTasks = useMemo(() =>
        tickets.filter(t => t.assignees.includes(currentUser.id) && (t.status === 'Received' || t.status === 'In Progress' || t.status === 'Reopened'))
        .sort((a,b) => b.urgency.localeCompare(a.urgency))
        .slice(0, 5)
    , [tickets, currentUser]);
    
    const recentActivity = useMemo(() => 
        tickets
            .sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
            .slice(0, 5)
            .map(ticket => ({
                id: ticket.id,
                text: `Ticket ${ticket.id} (${ticket.title}) was created.`,
                timestamp: ticket.createdAt,
            }))
    , [tickets]);


    return (
        <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
            {/* KPI Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <KPICard title="Total Tickets" value={kpiData.total} icon={<TicketIcon className="w-5 h-5 text-gray-500"/>} />
                <KPICard title="In Progress" value={kpiData.inProgress} icon={<Bars3Icon className="w-5 h-5 text-gray-500"/>} />
                <KPICard title="High Urgency" value={kpiData.highUrgency} icon={<ExclamationTriangleIcon className="w-5 h-5 text-red-500"/>} />
                <KPICard title="Unassigned" value={kpiData.unassigned} icon={<UserCircleIcon className="w-5 h-5 text-gray-500"/>} />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {/* Tickets by Status */}
                <div className="bg-white p-6 rounded-lg border border-gray-200 xl:col-span-1">
                    <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2"><ChartPieIcon className="w-5 h-5 text-gray-400" /> Tickets by Status</h3>
                    <div className="h-64">
                         <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                                <Pie data={statusChartData} dataKey="value" nameKey="name" cx="50%" cy="50%" innerRadius={60} outerRadius={80} paddingAngle={5} labelLine={false}>
                                    {statusChartData.map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={STATUS_COLORS[entry.name as TicketStatus]} />
                                    ))}
                                </Pie>
                                <Legend wrapperStyle={{fontSize: "12px"}} iconType="circle" />
                            </PieChart>
                        </ResponsiveContainer>
                    </div>
                </div>

                {/* Active High-Urgency Tickets */}
                <div className="bg-white p-6 rounded-lg border border-gray-200 xl:col-span-2">
                    <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2"><FireIcon className="w-5 h-5 text-red-500" /> Active High-Urgency Tickets</h3>
                    <div className="space-y-2">
                        {highUrgencyTickets.length > 0 ? highUrgencyTickets.map(ticket => <TicketListItem key={ticket.id} ticket={ticket} onClick={() => onSelectTicket(ticket.id)} />) : <p className="text-sm text-center text-gray-500 pt-8">No active high-urgency tickets.</p>}
                    </div>
                </div>

                {/* My Tasks */}
                 <div className="bg-white p-6 rounded-lg border border-gray-200 xl:col-span-1">
                    <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2"><UserCircleIcon className="w-5 h-5 text-gray-400" /> My Tasks</h3>
                     <div className="space-y-2">
                        {myTasks.length > 0 ? myTasks.map(ticket => <TicketListItem key={ticket.id} ticket={ticket} onClick={() => onSelectTicket(ticket.id)} />) : <p className="text-sm text-center text-gray-500 pt-8">You have no active tasks.</p>}
                    </div>
                </div>

                {/* Recent System Activity */}
                 <div className="bg-white p-6 rounded-lg border border-gray-200 xl:col-span-2">
                    <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2"><Bars3Icon className="w-5 h-5 text-gray-400" /> Recent System Activity</h3>
                     <div className="space-y-3">
                         {recentActivity.map(activity => (
                             <RecentActivityItem key={activity.id} activity={activity} onClick={() => onSelectTicket(activity.id)}/>
                         ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Dashboard;