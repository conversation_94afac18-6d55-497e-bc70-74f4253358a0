@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🧪 Daikin AI System - Automated Test Suite
echo ========================================
echo.

:: Set color
color 0B

:: Switch to project root directory
cd /d "%~dp0\.."

:: Check if system is running
echo 📋 Checking system status...
curl -s http://localhost:5173 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ System not running, please start system first
    echo Run: start-system.bat
    pause
    exit /b 1
)

echo ✅ System is running

:: Check Ollama service
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Ollama service not running
    pause
    exit /b 1
)

echo ✅ Ollama service normal

echo.
echo 🚀 Starting test suite execution...
echo.

:: Create test results directory
if not exist "test-results" mkdir test-results

:: Get current timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%"

echo 📊 Test report will be saved to: test-results\test-report-%timestamp%.txt

:: Start testing
echo Test start time: %date% %time% > test-results\test-report-%timestamp%.txt
echo ======================================== >> test-results\test-report-%timestamp%.txt

:: Test 1: Basic connection test
echo.
echo 🔗 Test 1: Basic Connection Test
echo [TEST 1] Basic Connection Test >> test-results\test-report-%timestamp%.txt

curl -s -o nul -w "HTTP Status: %%{http_code}, Response Time: %%{time_total}s" http://localhost:5173
if %errorlevel% equ 0 (
    echo ✅ Passed >> test-results\test-report-%timestamp%.txt
    echo ✅ Passed
) else (
    echo ❌ Failed >> test-results\test-report-%timestamp%.txt
    echo ❌ Failed
)

:: Test 2: AI service test
echo.
echo 🤖 Test 2: AI Service Connection Test
echo [TEST 2] AI Service Connection Test >> test-results\test-report-%timestamp%.txt

curl -s -X POST http://localhost:11434/api/generate -d "{\"model\":\"llama3.1:8b\",\"prompt\":\"Hello\",\"stream\":false}" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Passed >> test-results\test-report-%timestamp%.txt
    echo ✅ Passed
) else (
    echo ❌ Failed >> test-results\test-report-%timestamp%.txt
    echo ❌ Failed
)

:: Test 3: API endpoint test
echo.
echo 📡 Test 3: API Endpoint Test
echo [TEST 3] API Endpoint Test >> test-results\test-report-%timestamp%.txt

:: Test main pages
set "pages=/ /dashboard"
for %%p in (%pages%) do (
    curl -s -o nul -w "Page %%p - HTTP: %%{http_code}" http://localhost:5173%%p
    if !errorlevel! equ 0 (
        echo ✅ Page %%p normal >> test-results\test-report-%timestamp%.txt
    ) else (
        echo ❌ Page %%p failed >> test-results\test-report-%timestamp%.txt
    )
)

:: Test 4: Performance test
echo.
echo ⚡ Test 4: Performance Benchmark Test
echo [TEST 4] Performance Benchmark Test >> test-results\test-report-%timestamp%.txt

for /l %%i in (1,1,5) do (
    curl -s -o nul -w "Request %%i - Response Time: %%{time_total}s\n" http://localhost:5173
)

:: Test 5: System resource check
echo.
echo 💾 Test 5: System Resource Check
echo [TEST 5] System Resource Check >> test-results\test-report-%timestamp%.txt

:: Check Node.js process memory usage
for /f "tokens=5" %%a in ('tasklist /fi "imagename eq node.exe" /fo table ^| findstr node.exe') do (
    echo Node.js memory usage: %%a >> test-results\test-report-%timestamp%.txt
)

:: Generate test summary
echo.
echo ========================================
echo 📋 Test Completion Summary
echo ========================================
echo.

echo Test end time: %date% %time% >> test-results\test-report-%timestamp%.txt
echo ======================================== >> test-results\test-report-%timestamp%.txt

:: Display test results
type test-results\test-report-%timestamp%.txt

echo.
echo 📄 Detailed test report saved to: test-results\test-report-%timestamp%.txt
echo.

:: Ask if demo test should be run
echo.
set /p demo_test="Run demo scenario tests? (y/n): "
if /i "%demo_test%"=="y" (
    echo.
    echo 🎯 Starting demo scenario tests...
    echo.
    echo Please follow these steps for manual testing:
    echo.
    echo 1. Open browser and visit: http://localhost:5173
    echo 2. Test AI Assistant functionality
    echo 3. Test Smart Forms
    echo 4. Test Maintenance Dashboard
    echo 5. Test Predictive Analytics
    echo.
    echo For detailed test steps, refer to: TESTING_GUIDE.md
    echo.
    
    :: Open browser
    start http://localhost:5173
    
    :: Open test guide
    if exist "TD_Dev\TESTING_GUIDE.md" (
        start notepad "TD_Dev\TESTING_GUIDE.md"
    )
)

echo.
echo 🎉 Test script execution completed!
echo.
pause
