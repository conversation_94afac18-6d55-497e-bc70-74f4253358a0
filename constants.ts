
import type { User, Ticket, OrganizationNode, TicketStatus, TicketCategory, TicketUrgency, FormTemplate, Machine, Alert, ProductManual, EmployeeDirectoryEntry, KnowledgeArticle, Announcement, Policy, QuickLink, AIAssistantKnowledge } from './types';

export const USERS: User[] = [
  { id: 'su-1', name: 'Demo Admin', role: 'Super User', department: 'System', status: 'Online', workload: { urgent: 0, normal: 0 }, avatar: 'S', experience: { itSupport: 98, finance: 95, adminRequest: 92, marketing: 88, humanResources: 90, qualityAssurance: 94, operations: 96, rnd: 89 } },
  { id: 'u-1', name: '<PERSON>', role: 'CEO', department: 'Production', status: 'Online', workload: { urgent: 0, normal: 1 }, avatar: 'A', experience: { operations: 95, finance: 92, qualityAssurance: 88, rnd: 85 } },
  { id: 'u-2', name: '<PERSON>', role: 'Manager', department: 'Operations', status: 'Online', workload: { urgent: 1, normal: 3 }, avatar: 'B', experience: { operations: 94, qualityAssurance: 89, adminRequest: 87, itSupport: 82 } },
  { id: 'u-3', name: '<PERSON> <PERSON>', role: 'Manager', department: 'Marketing', status: 'On Business Trip', workload: { urgent: 0, normal: 2 }, avatar: 'C', experience: { marketing: 93, adminRequest: 88, humanResources: 85 } },
  { id: 'u-4', name: 'David Lee', role: 'Manager', department: 'IT', status: 'Online', workload: { urgent: 2, normal: 4 }, avatar: 'D', experience: { itSupport: 96, operations: 88, adminRequest: 91, rnd: 84 } },
  { id: 'u-5', name: 'Eve Williams', role: 'Technician', department: 'IT', status: 'Online', workload: { urgent: 1, normal: 8 }, avatar: 'E', experience: { itSupport: 92, operations: 85, adminRequest: 88 } },
  { id: 'u-6', name: 'Frank Miller', role: 'Technician', department: 'IT', status: 'On Leave', workload: { urgent: 0, normal: 0 }, avatar: 'F', experience: { itSupport: 89, operations: 82, adminRequest: 85 } },
  { id: 'u-7', name: 'Grace Davis', role: 'Manager', department: 'Assembly', status: 'On Business Trip', workload: { urgent: 1, normal: 2 }, avatar: 'G', experience: { operations: 91, qualityAssurance: 87, adminRequest: 84 } },
  { id: 'u-8', name: 'Heidi Brown', role: 'Worker', department: 'IT', status: 'Online', workload: { urgent: 1, normal: 4 }, avatar: 'H', experience: { itSupport: 78, adminRequest: 82, operations: 75 } },
  { id: 'u-9', name: 'Ivan Moore', role: 'Worker', department: 'IT', status: 'Online', workload: { urgent: 0, normal: 4 }, avatar: 'I', experience: { itSupport: 81, adminRequest: 79, operations: 77 } },
  { id: 'u-10', name: 'Judy White', role: 'Manager', department: 'Sales Team A', status: 'Online', workload: { urgent: 3, normal: 3 }, avatar: 'J', experience: { marketing: 90, adminRequest: 86, humanResources: 83 } },
  { id: 'u-11', name: 'Ken Purple', role: 'Manager', department: 'Sales Team B', status: 'Online', workload: { urgent: 0, normal: 5 }, avatar: 'K', experience: { marketing: 88, adminRequest: 84, humanResources: 81 } },
  { id: 'u-12', name: 'Laura Grey', role: 'Salesperson', department: 'Sales Team B', status: 'Online', workload: { urgent: 1, normal: 2 }, avatar: 'L', experience: { marketing: 82, adminRequest: 78 } },
  { id: 'u-13', name: 'Jack Black', role: 'Salesperson', department: 'Sales Team A', status: 'On Leave', workload: { urgent: 0, normal: 0 }, avatar: 'J', experience: { marketing: 85, adminRequest: 80 } },
  { id: 'u-14', name: 'Megan Green', role: 'Manager', department: 'Human Resources', status: 'Online', workload: { urgent: 1, normal: 1 }, avatar: 'M', experience: { humanResources: 94, adminRequest: 89, finance: 85 } },
  { id: 'u-15', name: 'Noah Orange', role: 'Manager', department: 'Finance', status: 'Online', workload: { urgent: 2, normal: 2 }, avatar: 'N', experience: { finance: 96, adminRequest: 91, humanResources: 87 } },
  { id: 'u-16', name: 'Olivia Violet', role: 'Manager', department: 'Quality Assurance', status: 'Online', workload: { urgent: 1, normal: 3 }, avatar: 'O', experience: { qualityAssurance: 93, operations: 89, rnd: 86 } },
  { id: 'u-17', name: 'Peter Indigo', role: 'QA Engineer', department: 'Quality Assurance', status: 'Online', workload: { urgent: 2, normal: 5 }, avatar: 'P', experience: { qualityAssurance: 91, operations: 87, rnd: 84 } },
  { id: 'u-18', name: 'Quinn Cyan', role: 'QA Engineer', department: 'Quality Assurance', status: 'On Business Trip', workload: { urgent: 0, normal: 2 }, avatar: 'Q', experience: { qualityAssurance: 88, operations: 84, rnd: 81 } },
  { id: 'u-19', name: 'Rachel Silver', role: 'Manager', department: 'R&D', status: 'Online', workload: { urgent: 1, normal: 4 }, avatar: 'R', experience: { rnd: 95, qualityAssurance: 90, operations: 87 } },
  { id: 'u-20', name: 'Sam Turquoise', role: 'Engineer', department: 'R&D', status: 'Online', workload: { urgent: 3, normal: 5 }, avatar: 'S', experience: { rnd: 92, qualityAssurance: 88, operations: 85, itSupport: 83 } },
  { id: 'u-21', name: 'Tina Gold', role: 'Engineer', department: 'R&D', status: 'Online', workload: { urgent: 1, normal: 6 }, avatar: 'T', experience: { rnd: 89, qualityAssurance: 85, operations: 82, itSupport: 80 } },
  { id: 'u-22', name: 'Uma Bronze', role: 'Technician', department: 'Operations', status: 'Online', workload: { urgent: 2, normal: 7 }, avatar: 'U', experience: { operations: 90, qualityAssurance: 86, adminRequest: 83 } },
  { id: 'u-23', name: 'Victor Platinum', role: 'Production Worker', department: 'Assembly', status: 'Online', workload: { urgent: 0, normal: 5 }, avatar: 'V', experience: { operations: 84, qualityAssurance: 80, adminRequest: 77 } },
  { id: 'u-24', name: 'Wendy Copper', role: 'Production Worker', department: 'Fabrication', status: 'On Leave', workload: { urgent: 0, normal: 0 }, avatar: 'W', experience: { operations: 82, qualityAssurance: 78, adminRequest: 75 } },
];

export const ORGANIZATION: OrganizationNode = {
  id: 'u-1', // CEO
  children: [
    { id: 'u-3' }, // Marketing
    { id: 'u-15' }, // Finance
    { id: 'u-14' }, // HR
    { id: 'u-4', children: [ {id: 'u-5'}, {id: 'u-6'}, {id: 'u-8'}, {id: 'u-9'} ] }, // IT
    {
      id: 'u-2', // Operations
      children: [
        { id: 'u-7', children: [ {id: 'u-23'}] }, // Assembly
        { id: 'u-16', children: [{id: 'u-17'}, {id: 'u-18'}]}, // QA
        { id: 'u-22'}, // Technician
        { id: 'u-24'}, // Fabrication Worker
      ]
    },
    { id: 'u-10', children: [{ id: 'u-13' }] }, // Sales A
    { id: 'u-11', children: [{ id: 'u-12' }] }, // Sales B
    { id: 'u-19', children: [{ id: 'u-20' }, { id: 'u-21' }] } // R&D
  ]
};

export const TICKET_STATUSES: TicketStatus[] = ['Received', 'In Progress', 'Waiting Feedback', 'Done', 'Reopened', 'Canceled', 'Closed'];
export const TICKET_CATEGORIES: TicketCategory[] = ['IT Support', 'Finance', 'Admin Request', 'Marketing', 'Human Resources', 'Quality Assurance', 'Operations', 'R&D'];
export const TICKET_URGENCIES: TicketUrgency[] = ['Low', 'Medium', 'High'];

export const STATUS_COLORS: { [key in TicketStatus]: string } = {
  Received: '#3b82f6', // blue-500
  'In Progress': '#8b5cf6', // violet-500
  Done: '#16a34a', // green-600
  Reopened: '#db2777', // pink-600
  'Waiting Feedback': '#f97316', // orange-500
  Canceled: '#dc2626', // red-600
  Closed: '#6b7280', // gray-500
};


export const INITIAL_TICKETS: Ticket[] = [
    { id: 'TICK-14', title: 'Annual Performance Review Process', status: 'Received', category: 'Human Resources', urgency: 'Medium', assignees: ['u-14'], createdAt: '2024-07-31', activity: [], description: 'Finalize and document the annual performance review process for all departments.' },
    { id: 'TICK-10', title: 'Review Q3 Strategic Acquisition Proposal', status: 'Received', category: 'Finance', urgency: 'High', assignees: ['u-15', 'u-1'], createdAt: '2024-07-31', activity: [], description: 'A detailed review of the strategic acquisition proposal for Project Phoenix is required by the finance committee.' },
    { id: 'TICK-13', title: 'Order New Office Stationery', status: 'Received', category: 'Admin Request', urgency: 'Low', assignees: [], createdAt: '2024-07-30', activity: [], description: 'The marketing department has run out of branded letterheads and envelopes. Need to order a new batch.' },
    { id: 'TICK-6', title: 'Quarterly Financial Report', status: 'In Progress', category: 'Finance', urgency: 'High', assignees: ['u-15'], createdAt: '2024-07-29', activity: [], description: 'Prepare and finalize the quarterly financial report for the board meeting.' },
    { id: 'TICK-1', title: 'Setup Backend Authentication', status: 'In Progress', category: 'IT Support', urgency: 'High', assignees: ['u-4', 'u-5'], createdAt: '2024-07-28', activity: [], description: 'Implement JWT-based authentication for the new customer portal API.' },
    { id: 'TICK-3', title: 'Fix Database Connection Pool Leak', status: 'In Progress', category: 'IT Support', urgency: 'High', assignees: ['u-5', 'u-8'], createdAt: '2024-07-28', activity: [], description: 'The production database is experiencing connection pool exhaustion. A memory leak is suspected. This is a critical issue affecting all services.' },
    { id: 'TICK-15', title: 'Investigate Production Line Anomaly #PL-7', status: 'Received', category: 'Quality Assurance', urgency: 'High', assignees: ['u-16', 'u-17'], createdAt: '2024-08-01', activity: [], description: 'Production Line 7 reported a 15% increase in product defects over the last 24 hours. Immediate investigation required.' },
    { id: 'TICK-16', title: 'Deploy Security Patch to Web Servers', status: 'In Progress', category: 'IT Support', urgency: 'High', assignees: ['u-4', 'u-9'], createdAt: '2024-08-01', activity: [], description: 'A critical security vulnerability (CVE-2024-12345) was announced. All public-facing web servers must be patched within 4 hours.' },
    { id: 'TICK-17', title: 'New "Peace of Mind" Campaign Creatives', status: 'Waiting Feedback', category: 'Marketing', urgency: 'Medium', assignees: ['u-3'], createdAt: '2024-07-30', activity: [], description: 'The first draft of the "Peace of Mind" campaign creatives has been delivered. Awaiting feedback from the CEO.' },
    { id: 'TICK-18', title: 'Sales Commission Calculation Error', status: 'Received', category: 'Finance', urgency: 'High', assignees: ['u-15'], createdAt: '2024-08-02', activity: [], description: 'Multiple salespeople from Sales Team A have reported discrepancies in their July commission payments.' },
    { id: 'TICK-19', title: 'Test Plan for New Mobile App Feature', status: 'Received', category: 'Quality Assurance', urgency: 'Medium', assignees: ['u-18'], createdAt: '2024-08-02', activity: [], description: 'Please create a comprehensive test plan for the new "Smart Scheduling" feature in the customer mobile app.' },
    { id: 'TICK-11', title: 'Finalize Assembly Line QA Procedures', status: 'Done', category: 'Quality Assurance', urgency: 'Medium', assignees: ['u-16'], createdAt: '2024-07-28', activity: [], description: 'Document and implement the final quality assurance testing procedures for the new assembly line.' },
    { id: 'TICK-2', title: 'Design New Landing Page', status: 'Done', category: 'Marketing', urgency: 'Medium', assignees: ['u-3'], createdAt: '2024-07-26', activity: [], description: 'Create a new landing page design for the upcoming "Summer Sale" marketing campaign.' },
    { id: 'TICK-5', title: 'User Feedback on new Dashboard', status: 'Waiting Feedback', category: 'Marketing', urgency: 'Medium', assignees: ['u-10'], createdAt: '2024-07-26', activity: [], description: 'Collect and analyze user feedback from the beta testers of the new analytics dashboard.' },
    { id: 'TICK-4', title: 'Refactor User Profile Component', status: 'Done', category: 'IT Support', urgency: 'Low', assignees: ['u-9'], createdAt: '2024-07-25', activity: [], description: 'The user profile component in the main app is using outdated libraries. It needs to be refactored to use the new design system and state management.' },
    { id: 'TICK-12', title: 'Resolve Customer Complaint #8842', status: 'Reopened', category: 'Marketing', urgency: 'High', assignees: ['u-12', 'u-3'], createdAt: '2024-07-25', activity: [], description: 'A high-priority customer complaint (Ticket #8842) regarding a billing discrepancy needs to be resolved immediately.' },
    { id: 'TICK-8', title: 'Update Company Holiday Calendar', status: 'Canceled', category: 'Admin Request', urgency: 'Low', assignees: [], createdAt: '2024-07-22', activity: [], description: 'Update the shared company calendar with the new list of public holidays for 2025. This was canceled as HR decided to do it.' },
    { id: 'TICK-7', title: 'Onboard New Sales Hires', status: 'Closed', category: 'Human Resources', urgency: 'Medium', assignees: ['u-14'], createdAt: '2024-07-20', activity: [], description: 'Prepare onboarding materials and schedule training sessions for the three new sales hires starting next Monday.' },
    { id: 'TICK-20', title: 'Cannot Access Shared Network Drive', status: 'Received', category: 'IT Support', urgency: 'Medium', assignees: [], createdAt: '2024-08-03', activity: [], description: 'I am unable to access the Marketing shared drive (M:). When I try to open it, I receive an "access denied" error. I have already tried restarting my computer.' },
    { id: 'TICK-21', title: 'Calibrate CNC Machine #M-003', status: 'In Progress', category: 'Operations', urgency: 'High', assignees: ['u-22'], createdAt: '2024-08-04', activity: [], description: 'CNC Router C (M-003) is reporting drift on the X-axis. Needs immediate recalibration to prevent further production defects.' },
    { id: 'TICK-22', title: 'Research new Titanium alloy for compressors', status: 'Received', category: 'R&D', urgency: 'Medium', assignees: ['u-19', 'u-20'], createdAt: '2024-08-04', activity: [], description: 'Feasibility study for using a new lightweight titanium alloy in next-generation compressor blades to improve efficiency.' },
    { id: 'TICK-23', title: 'VPN connection issues for remote employees', status: 'In Progress', category: 'IT Support', urgency: 'Medium', assignees: ['u-5'], createdAt: '2024-08-04', activity: [], description: 'Multiple remote users have reported intermittent disconnections from the corporate VPN since the last network update.' },
    { id: 'TICK-24', title: 'Update forklift safety protocols', status: 'Done', category: 'Operations', urgency: 'Low', assignees: ['u-2'], createdAt: '2024-08-01', activity: [], description: 'Review and update the forklift operation safety manual for the warehouse and production floors.' },
    { id: 'TICK-25', title: 'Replace HVAC filters in Building A', status: 'Received', category: 'Operations', urgency: 'Medium', assignees: [], createdAt: '2024-08-05', activity: [], description: 'Quarterly maintenance: Replace all HVAC filters in Building A. Some filters are showing signs of excessive dust accumulation.' },
    { id: 'TICK-26', title: 'Software license renewal for CAD tools', status: 'Received', category: 'IT Support', urgency: 'High', assignees: ['u-5'], createdAt: '2024-08-05', activity: [], description: 'AutoCAD and SolidWorks licenses expire in 2 weeks. Need to process renewal to avoid disruption to engineering team.' },
    { id: 'TICK-27', title: 'Employee wellness program proposal', status: 'Received', category: 'Human Resources', urgency: 'Low', assignees: ['u-14'], createdAt: '2024-08-05', activity: [], description: 'Develop a comprehensive employee wellness program including fitness facilities, mental health support, and nutrition guidance.' },
    { id: 'TICK-28', title: 'Machine M-007 temperature alarm', status: 'In Progress', category: 'Operations', urgency: 'High', assignees: ['u-22'], createdAt: '2024-08-05', activity: [], description: 'Injection Molding Machine M-007 triggered a high temperature alarm. Currently investigating cooling system malfunction.' },
    { id: 'TICK-29', title: 'Update product catalog for Q4', status: 'Received', category: 'Marketing', urgency: 'Medium', assignees: ['u-3'], createdAt: '2024-08-05', activity: [], description: 'Update the product catalog with new VRV models and discontinue legacy products for Q4 release.' },
];

export const INITIAL_FORM_TEMPLATES: FormTemplate[] = [
    {
        id: 'tpl-it-support',
        name: 'IT Support Request',
        description: 'Submit a ticket for technical issues like software problems, hardware failures, or network access.',
        fields: [
            { name: 'summary', label: 'Summary of the issue', type: 'text', required: true },
            { name: 'description', label: 'Full description of the problem (include error messages)', type: 'textarea', required: true },
            { name: 'urgency', label: 'Urgency', type: 'select', required: true, options: ['Low', 'Medium', 'High'] },
            { name: 'attachment', label: 'Attachment (optional, e.g., screenshot)', type: 'file', required: false },
        ]
    },
    {
        id: 'tpl-leave-request',
        name: 'Leave Request',
        description: 'Formally request time off for vacation, sick leave, or other personal reasons.',
        fields: [
            { name: 'leave_type', label: 'Type of Leave', type: 'select', required: true, options: ['Vacation', 'Sick Leave', 'Personal', 'Other'] },
            { name: 'start_date', label: 'Start Date', type: 'date', required: true },
            { name: 'end_date', label: 'End Date', type: 'date', required: true },
            { name: 'reason', label: 'Reason for leave (optional)', type: 'textarea', required: false },
        ]
    },
     {
        id: 'tpl-purchase-request',
        name: 'Purchase Request',
        description: 'Request approval for new purchases of software, hardware, or office supplies.',
        fields: [
            { name: 'item_name', label: 'Item Name / Service', type: 'text', required: true },
            { name: 'quantity', label: 'Quantity', type: 'number', required: true },
            { name: 'unit_price', label: 'Estimated Price per Unit ($)', type: 'number', required: true },
            { name: 'justification', label: 'Business Justification', type: 'textarea', required: true },
        ]
    },
    {
        id: 'tpl-hardware-return',
        name: 'Hardware Return',
        description: 'Initiate the process for returning company-issued hardware (e.g., laptop, phone) upon employment termination or upgrade.',
        fields: [
            { name: 'employee_name', label: 'Employee Name', type: 'text', required: true },
            { name: 'employee_id', label: 'Employee ID', type: 'text', required: true },
            { name: 'hardware_type', label: 'Type of Hardware', type: 'select', required: true, options: ['Laptop', 'Mobile Phone', 'Tablet', 'Other'] },
            { name: 'asset_tag', label: 'Asset Tag Number', type: 'text', required: true },
            { name: 'return_date', label: 'Date of Return', type: 'date', required: true },
        ]
    },
    {
        id: 'tpl-safety-incident',
        name: 'Safety Incident Report',
        description: 'Report workplace safety incidents, near misses, or hazardous conditions.',
        fields: [
            { name: 'incident_type', label: 'Type of Incident', type: 'select', required: true, options: ['Injury', 'Near Miss', 'Property Damage', 'Hazardous Condition'] },
            { name: 'incident_date', label: 'Date of Incident', type: 'date', required: true },
            { name: 'location', label: 'Location', type: 'text', required: true },
            { name: 'description', label: 'Detailed Description', type: 'textarea', required: true },
            { name: 'witnesses', label: 'Witnesses (if any)', type: 'text', required: false },
            { name: 'immediate_action', label: 'Immediate Action Taken', type: 'textarea', required: false },
        ]
    },
    {
        id: 'tpl-maintenance-request',
        name: 'Maintenance Request',
        description: 'Request maintenance or repair for equipment, facilities, or infrastructure.',
        fields: [
            { name: 'equipment_id', label: 'Equipment/Machine ID', type: 'text', required: true },
            { name: 'issue_type', label: 'Type of Issue', type: 'select', required: true, options: ['Preventive Maintenance', 'Repair', 'Calibration', 'Inspection'] },
            { name: 'priority', label: 'Priority Level', type: 'select', required: true, options: ['Low', 'Medium', 'High', 'Emergency'] },
            { name: 'description', label: 'Problem Description', type: 'textarea', required: true },
            { name: 'fault_code', label: 'Fault Code (if displayed)', type: 'text', required: false },
        ]
    },
    {
        id: 'tpl-training-request',
        name: 'Training Request',
        description: 'Request training for yourself or your team members.',
        fields: [
            { name: 'training_type', label: 'Type of Training', type: 'select', required: true, options: ['Technical Skills', 'Safety Training', 'Software Training', 'Leadership Development', 'Other'] },
            { name: 'participants', label: 'Number of Participants', type: 'number', required: true },
            { name: 'preferred_date', label: 'Preferred Training Date', type: 'date', required: false },
            { name: 'justification', label: 'Business Justification', type: 'textarea', required: true },
            { name: 'budget_estimate', label: 'Estimated Budget ($)', type: 'number', required: false },
        ]
    },
    {
        id: 'tpl-vendor-request',
        name: 'New Vendor Request',
        description: 'Request approval to work with a new vendor or supplier.',
        fields: [
            { name: 'vendor_name', label: 'Vendor Company Name', type: 'text', required: true },
            { name: 'contact_person', label: 'Primary Contact Person', type: 'text', required: true },
            { name: 'service_type', label: 'Type of Service/Product', type: 'text', required: true },
            { name: 'estimated_value', label: 'Estimated Annual Value ($)', type: 'number', required: true },
            { name: 'justification', label: 'Business Justification', type: 'textarea', required: true },
        ]
    }
];

// --- New Maintenance Data ---
const generateTrend = (base: number, points: number, variance: number): { timestamp: string; value: number }[] => {
  let value = base;
  return Array.from({ length: points }, (_, i) => {
    const date = new Date();
    date.setHours(date.getHours() - (points - i));
    value += (Math.random() - 0.5) * variance;
    if (value < 0) value = 0;
    return { timestamp: date.toISOString(), value: parseFloat(value.toFixed(1)) };
  });
};

export const MAINTENANCE_DATA: { machines: Machine[], alerts: Alert[] } = {
  machines: [
    { id: 'M-001', name: 'Compressor A', zone: 'Zone A', machineType: 'Air Compressor', protocol: 'MQTT', status: 'Operational', temp: 58.9, vibration: 13.7, pressure: 120.5, humidity: 45.2, faultCode: null, healthScore: 98, trends: { temperature: generateTrend(60, 24, 2), vibration: generateTrend(12, 24, 1), pressure: generateTrend(120, 24, 5), humidity: generateTrend(45, 24, 2) } },
    { id: 'M-002', name: 'Hydraulic Press B', zone: 'Zone A', machineType: 'Hydraulic Press', protocol: 'Modbus', status: 'Warning', temp: 62.4, vibration: 9.9, pressure: 2950.0, humidity: 55.8, faultCode: 'F-034', healthScore: 73, trends: { temperature: generateTrend(65, 24, 5), vibration: generateTrend(10, 24, 0.5), pressure: generateTrend(3000, 24, 100), humidity: generateTrend(55, 24, 5) } },
    { id: 'M-003', name: 'CNC Router C', zone: 'Zone B', machineType: 'CNC Machine', protocol: 'OPC-UA', status: 'Failure', temp: 89.8, vibration: 14.7, pressure: 85.3, humidity: 62.1, faultCode: 'F-102', healthScore: 45, trends: { temperature: generateTrend(90, 24, 10), vibration: generateTrend(15, 24, 3), pressure: generateTrend(90, 24, 10), humidity: generateTrend(60, 24, 8) } },
    { id: 'M-004', name: 'Cooling Fan D', zone: 'Zone B', machineType: 'Ventilation', protocol: 'MQTT', status: 'Operational', temp: 56.2, vibration: 11.7, pressure: 101.2, humidity: 48.9, faultCode: null, healthScore: 95, trends: { temperature: generateTrend(55, 24, 1), vibration: generateTrend(11, 24, 0.8), pressure: generateTrend(100, 24, 2), humidity: generateTrend(50, 24, 3) } },
    { id: 'M-005', name: 'Welding Robot E', zone: 'Zone C', machineType: 'Robotics', protocol: 'OPC-UA', status: 'Operational', temp: 70.1, vibration: 8.2, pressure: 95.0, humidity: 40.3, faultCode: null, healthScore: 99, trends: { temperature: generateTrend(70, 24, 3), vibration: generateTrend(8, 24, 0.5), pressure: generateTrend(95, 24, 2), humidity: generateTrend(40, 24, 4) } },
    { id: 'M-006', name: 'Packaging Line F', zone: 'Zone C', machineType: 'Conveyor', protocol: 'Modbus', status: 'Operational', temp: 45.5, vibration: 12.5, pressure: 110.0, humidity: 51.5, faultCode: null, healthScore: 92, trends: { temperature: generateTrend(45, 24, 2), vibration: generateTrend(12, 24, 1), pressure: generateTrend(110, 24, 3), humidity: generateTrend(50, 24, 2) } },
    { id: 'M-007', name: 'Chiller Unit G', zone: 'Zone A', machineType: 'HVAC', protocol: 'MQTT', status: 'Warning', temp: 5.2, vibration: 7.8, pressure: 210.0, humidity: 85.0, faultCode: 'F-015', healthScore: 78, trends: { temperature: generateTrend(5, 24, 1), vibration: generateTrend(8, 24, 0.7), pressure: generateTrend(200, 24, 15), humidity: generateTrend(85, 24, 5) } },
  ],
  alerts: [
    { id: 'A-01', machineName: 'CNC Router C', message: 'Vibration exceeded critical threshold (14.5 mm/s)', timestamp: new Date().toISOString(), type: 'Failure' },
    { id: 'A-02', machineName: 'Hydraulic Press B', message: 'Health score dropped to 73%. Possible valve malfunction.', timestamp: new Date(Date.now() - 3600000).toISOString(), type: 'Warning' },
    { id: 'A-03', machineName: 'Chiller Unit G', message: 'Coolant pressure is higher than expected.', timestamp: new Date(Date.now() - 7200000).toISOString(), type: 'Warning' },
    { id: 'A-04', machineName: 'Compressor A', message: 'Temperature sensor calibration required. Current reading may be inaccurate.', timestamp: new Date(Date.now() - 10800000).toISOString(), type: 'Warning' },
    { id: 'A-05', machineName: 'Welding Robot E', message: 'Scheduled maintenance due in 48 hours. Please prepare maintenance checklist.', timestamp: new Date(Date.now() - 14400000).toISOString(), type: 'Warning' },
    { id: 'A-06', machineName: 'Packaging Line F', message: 'Conveyor belt tension adjustment needed. Detected slight misalignment.', timestamp: new Date(Date.now() - 18000000).toISOString(), type: 'Warning' },
    { id: 'A-07', machineName: 'CNC Router C', message: 'Emergency stop activated. Machine requires manual reset and inspection.', timestamp: new Date(Date.now() - 21600000).toISOString(), type: 'Failure' },
    { id: 'A-08', machineName: 'Cooling Fan D', message: 'Fan speed fluctuation detected. Check motor bearings and electrical connections.', timestamp: new Date(Date.now() - 25200000).toISOString(), type: 'Warning' },
    { id: 'A-09', machineName: 'Hydraulic Press B', message: 'Hydraulic fluid level low. Refill required before next operation cycle.', timestamp: new Date(Date.now() - 28800000).toISOString(), type: 'Warning' },
    { id: 'A-10', machineName: 'Chiller Unit G', message: 'Refrigerant leak detected in secondary circuit. Immediate attention required.', timestamp: new Date(Date.now() - 32400000).toISOString(), type: 'Failure' },
    { id: 'A-11', machineName: 'Compressor A', message: 'Air filter replacement overdue by 72 hours. Performance degradation expected.', timestamp: new Date(Date.now() - 36000000).toISOString(), type: 'Warning' },
    { id: 'A-12', machineName: 'Welding Robot E', message: 'Welding torch tip wear detected. Replace tip to maintain weld quality.', timestamp: new Date(Date.now() - 39600000).toISOString(), type: 'Warning' },
  ]
};

// --- New Wiki Data ---
export const WIKI_ANNOUNCEMENTS: Announcement[] = [
    {
        id: 'AN-01',
        title: 'Launch of New Global Brand Campaign "Perfecting the Air"',
        date: '2024-08-01',
        author: 'Sarah Chen',
        authorDepartment: 'Marketing',
        content: 'We are thrilled to launch our new global brand campaign, "Perfecting the Air," which highlights our commitment to innovation, sustainability, and customer well-being. The campaign will roll out across all regions starting this month.',
        priority: 'High',
        attachments: [
            {
                id: 'att-an01-1',
                name: 'Campaign_Guidelines.pdf',
                type: 'application/pdf',
                size: 2048000,
                url: '#',
                dataType: 'unstructured',
                analysisResult: {
                    confidence: 0.92,
                    reasoning: 'PDF document contains unstructured marketing content with text and images.',
                    detectedFormat: 'PDF',
                    structureElements: ['Headings', 'Paragraphs', 'Images', 'Brand Guidelines']
                }
            }
        ]
    },
    {
        id: 'AN-02',
        title: 'Q3 Financial Results and Outlook',
        date: '2024-07-28',
        author: 'Michael Rodriguez',
        authorDepartment: 'Finance',
        content: 'Daikin has posted strong results for the third quarter, driven by robust demand in North America and Southeast Asia. The full report is now available on the investor relations portal.',
        priority: 'Medium',
        attachments: [
            {
                id: 'att-an02-1',
                name: 'Q3_Financial_Report.xlsx',
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                size: 1536000,
                url: '#',
                dataType: 'structured',
                analysisResult: {
                    confidence: 0.95,
                    reasoning: 'Excel spreadsheet contains structured financial data with defined columns and formulas.',
                    detectedFormat: 'XLSX',
                    structureElements: ['Tables', 'Columns', 'Rows', 'Formulas', 'Charts']
                }
            }
        ]
    },
    {
        id: 'AN-03',
        title: 'Annual Employee Engagement Survey',
        date: '2024-08-05',
        author: 'Jennifer Park',
        authorDepartment: 'Human Resources',
        content: 'The annual employee engagement survey is now live. Your feedback is crucial for making AIT Systems a better place to work. Please complete the survey by August 20th.',
        priority: 'Medium',
        attachments: []
    },
];

export const WIKI_QUICK_LINKS: QuickLink[] = [
    { id: 'QL-01', title: 'HR Portal', url: '#', icon: 'UserGroupIcon' },
    { id: 'QL-03', title: 'IT Service Desk', appTarget: 'smart-form', icon: 'TicketIcon' },
    { id: 'QL-02', title: 'Machine Dashboard', appTarget: 'maintenance', icon: 'WrenchScrewdriverIcon' },
    { id: 'QL-04', title: 'Ask AI Assistant', appTarget: 'ai-assistant', icon: 'BotIcon' },
];

export const WIKI_PRODUCT_MANUALS: ProductManual[] = [
    { id: 'PM-01', name: 'VRV IV-S Series (RXSQ-TVJU)', category: 'VRV Systems', modelNumbers: ['RXSQ24TVJU', 'RXSQ36TVJU', 'RXSQ48TVJU'], summary: 'Compact, efficient outdoor units for residential and light commercial applications. Offers superior energy savings and flexible installation.', documentUrl: '#', },
    { id: 'PM-02', name: 'Daikin EMURA (FTXG-L)', category: 'Split/Multi-Split', modelNumbers: ['FTXG09LVJUW', 'FTXG12LVJUW', 'FTXG18LVJUS'], summary: 'Award-winning design combined with smart technology. Features intelligent eye sensor and Wi-Fi control.', documentUrl: '#', },
    { id: 'PM-03', name: 'Pathfinder® Air-Cooled Chiller (AWV)', category: 'Applied/Chillers', modelNumbers: ['AWV 140', 'AWV 210', 'AWV 350'], summary: 'Industry-leading air-cooled screw chiller with Variable Volume Ratio (VVR) technology for optimized part-load efficiency.', documentUrl: '#', },
    { id: 'PM-04', name: 'MC55W Air Purifier', category: 'Air Purifiers', modelNumbers: ['MC55W-W'], summary: 'Features Daikin’s Streamer Technology to decompose viruses, bacteria, and allergens. Compact and quiet design.', documentUrl: '#', },
    { id: 'PM-05', name: 'Daikin ONE+ Smart Thermostat', category: 'Split/Multi-Split', modelNumbers: ['DTST-ONE-ADA-A'], summary: 'A smart thermostat for temperature and humidity control, providing a complete solution for single and multi-zone systems.', documentUrl: '#', },
];

export const WIKI_EMPLOYEE_DIRECTORY: EmployeeDirectoryEntry[] = [
    { id: 'u-5', name: 'Eve Williams', role: 'IT Support Specialist', department: 'IT', email: '<EMAIL>', phone: 'x5512', location: 'HQ', avatar: 'E' },
    { id: 'u-17', name: 'Peter Indigo', role: 'Senior QA Engineer', department: 'Quality Assurance', email: '<EMAIL>', phone: 'x8891', location: 'Tech Center', avatar: 'P' },
    { id: 'u-2', name: 'Bob Smith', role: 'Production Manager', department: 'Production', email: '<EMAIL>', phone: 'x2345', location: 'Factory A', avatar: 'B' },
];

export const WIKI_KNOWLEDGE_ARTICLES: KnowledgeArticle[] = [
    {
        id: 'KA-01',
        title: 'Our Philosophy: People-Centered Management',
        category: 'Company Philosophy',
        author: 'Alice Johnson',
        authorDepartment: 'Production',
        publishDate: '2024-01-15',
        tags: ['Culture', 'HR', 'Management'],
        content: 'Daikin\'s corporate philosophy is built upon the core principle of "People-Centered Management." We believe that the source of a company\'s competitiveness lies in its people. Our goal is to create an environment where each employee can work with enthusiasm and purpose, exercise their full potential, and achieve personal growth. \n\nThis philosophy manifests in several key practices:\n\n- **Empowerment and Trust:** We entrust employees with significant responsibilities and provide them with the autonomy to make decisions. This fosters a sense of ownership and encourages proactive problem-solving.\n- **Continuous Learning:** We invest heavily in employee training and development, offering a wide range of programs to enhance skills and knowledge.\n- **Open Communication:** We maintain an open and transparent work environment where opinions can be freely exchanged, regardless of an individual\'s position in the hierarchy.',
        attachments: [
            {
                id: 'att-ka01-1',
                name: 'Management_Philosophy_Guide.docx',
                type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                size: 1024000,
                url: '#',
                dataType: 'unstructured',
                analysisResult: {
                    confidence: 0.88,
                    reasoning: 'Word document contains unstructured text content with headings and paragraphs.',
                    detectedFormat: 'DOCX',
                    structureElements: ['Headings', 'Paragraphs', 'Lists', 'Text Formatting']
                }
            }
        ]
    },
    {
        id: 'KA-02',
        title: 'Introduction to Inverter Technology',
        category: 'Engineering Best Practices',
        author: 'Sam Turquoise',
        authorDepartment: 'R&D',
        publishDate: '2024-05-20',
        tags: ['Technology', 'Inverter', 'HVAC'],
        content: 'Inverter technology is a cornerstone of modern, energy-efficient HVAC systems. Unlike traditional fixed-speed systems that operate on a simple on/off cycle, an inverter-equipped system can continuously adjust its compressor speed. By varying the speed, the system can precisely match the cooling or heating load required at any given moment. This results in significant energy savings, as the compressor does not need to run at full power all the time. Furthermore, it provides superior comfort by maintaining a more consistent temperature, eliminating the drastic fluctuations common with older systems.',
        attachments: [
            {
                id: 'att-ka02-1',
                name: 'Inverter_Technical_Specs.xlsx',
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                size: 2560000,
                url: '#',
                dataType: 'structured',
                analysisResult: {
                    confidence: 0.94,
                    reasoning: 'Excel file contains structured technical data with specifications and performance metrics.',
                    detectedFormat: 'XLSX',
                    structureElements: ['Tables', 'Columns', 'Technical Data', 'Charts', 'Formulas']
                }
            },
            {
                id: 'att-ka02-2',
                name: 'Inverter_Diagram.pdf',
                type: 'application/pdf',
                size: 1792000,
                url: '#',
                dataType: 'unstructured',
                analysisResult: {
                    confidence: 0.89,
                    reasoning: 'PDF contains technical diagrams and explanatory text.',
                    detectedFormat: 'PDF',
                    structureElements: ['Diagrams', 'Technical Drawings', 'Annotations', 'Text']
                }
            }
        ]
    },
    {
        id: 'KA-03',
        title: 'Next-Generation Refrigerant R-32',
        category: 'R&D',
        author: 'Rachel Silver',
        authorDepartment: 'R&D',
        publishDate: '2024-06-30',
        tags: ['Sustainability', 'Refrigerant', 'R-32'],
        content: 'As a global leader in air conditioning, Daikin is committed to environmental sustainability. A key part of this commitment is our pioneering role in the adoption of R-32 refrigerant. R-32 is a next-generation refrigerant that offers a compelling balance of performance and environmental friendliness. Its Global Warming Potential (GWP) is approximately one-third that of the widely used R-410A refrigerant. Additionally, R-32 is more efficient, allowing for a smaller refrigerant charge in systems and contributing to reduced indirect CO2 emissions from electricity consumption. Daikin is actively promoting the use of R-32 globally to mitigate the impact of the HVAC industry on climate change.',
        attachments: []
    },
     {
        id: 'KA-04',
        title: 'The Fusion 25 Management Plan',
        category: 'Company Philosophy',
        author: 'Alice Johnson',
        authorDepartment: 'Production',
        publishDate: '2024-02-10',
        tags: ['Strategy', 'Growth', 'Fusion 25'],
        content: 'Fusion 25 is our strategic management plan designed to guide Daikin through 2025. It focuses on achieving both business growth and a significant contribution to a sustainable society. The "Fusion" name signifies the merging of our core strengths with new technologies and collaborative partnerships. Key pillars include:\n\n1.  **Challenge to Achieve Carbon Neutrality:** Accelerating our efforts in developing energy-efficient products and promoting heat pump technology.\n2.  **Creation of Value with Air:** Expanding our business into new domains related to air quality, comfort, and health.\n3.  **Enhancing the Value of "People-Centered Management":** Further developing our global workforce to drive innovation and growth.',
        attachments: [
            {
                id: 'att-ka04-1',
                name: 'Fusion25_Strategy_Document.pdf',
                type: 'application/pdf',
                size: 3072000,
                url: '#',
                dataType: 'unstructured',
                analysisResult: {
                    confidence: 0.91,
                    reasoning: 'PDF document contains strategic planning content with text, charts, and executive summary.',
                    detectedFormat: 'PDF',
                    structureElements: ['Executive Summary', 'Strategic Goals', 'Charts', 'Timeline']
                }
            }
        ]
    },
];

export const WIKI_POLICIES: Policy[] = [
    {
        id: 'POL-01',
        title: 'Employee Code of Conduct',
        category: 'HR',
        summary: 'Outlines the expected standards of behavior for all employees, covering professionalism, ethics, and integrity.',
        documentUrl: '#',
        effectiveDate: '2024-01-01',
        lastUpdated: '2024-01-01',
        author: 'Megan Green',
        authorDepartment: 'Human Resources',
        attachments: [
            {
                id: 'att-pol01-1',
                name: 'Code_of_Conduct_Full.pdf',
                type: 'application/pdf',
                size: 1536000,
                url: '#',
                dataType: 'unstructured',
                analysisResult: {
                    confidence: 0.87,
                    reasoning: 'PDF policy document with structured sections but primarily text-based content.',
                    detectedFormat: 'PDF',
                    structureElements: ['Policy Sections', 'Guidelines', 'Examples', 'References']
                }
            }
        ]
    },
    {
        id: 'POL-02',
        title: 'Data Security & Privacy Policy',
        category: 'IT Security',
        summary: 'Defines the procedures and responsibilities for protecting company and customer data.',
        documentUrl: '#',
        effectiveDate: '2024-02-15',
        lastUpdated: '2024-02-15',
        author: 'David Lee',
        authorDepartment: 'IT',
        attachments: [
            {
                id: 'att-pol02-1',
                name: 'Security_Procedures_Checklist.xlsx',
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                size: 768000,
                url: '#',
                dataType: 'structured',
                analysisResult: {
                    confidence: 0.93,
                    reasoning: 'Excel checklist with structured security procedures and compliance requirements.',
                    detectedFormat: 'XLSX',
                    structureElements: ['Checklists', 'Procedures', 'Compliance Items', 'Status Tracking']
                }
            }
        ]
    },
    {
        id: 'POL-03',
        title: 'Travel and Expense Reimbursement',
        category: 'Finance',
        summary: 'Guidelines for booking travel and submitting expenses for reimbursement.',
        documentUrl: '#',
        effectiveDate: '2024-03-01',
        lastUpdated: '2024-03-01',
        author: 'Noah Orange',
        authorDepartment: 'Finance',
        attachments: []
    },
    {
        id: 'POL-04',
        title: 'Workplace Safety Standards',
        category: 'Operations',
        summary: 'Mandatory safety procedures for all factory and on-site personnel.',
        documentUrl: '#',
        effectiveDate: '2024-01-15',
        lastUpdated: '2024-01-15',
        author: 'Bob Smith',
        authorDepartment: 'Operations',
        attachments: [
            {
                id: 'att-pol04-1',
                name: 'Safety_Incident_Report_Template.xlsx',
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                size: 512000,
                url: '#',
                dataType: 'structured',
                analysisResult: {
                    confidence: 0.96,
                    reasoning: 'Excel template with structured form fields for incident reporting.',
                    detectedFormat: 'XLSX',
                    structureElements: ['Form Fields', 'Data Validation', 'Dropdown Lists', 'Calculations']
                }
            }
        ]
    },
];


// --- New AI Assistant Knowledge Base ---
export const AI_ASSISTANT_KNOWLEDGE_BASE: AIAssistantKnowledge[] = [
    {
        id: 'AIK-01',
        question: "What is our company's policy on remote work?",
        answer: "Our company supports a flexible, hybrid work model. Employees are generally expected to be in the office 3 days a week, with the specific days determined by team managers to ensure collaboration. Fully remote positions are available in certain roles and require approval from department heads.",
        source: "HR Policy: Flexible Work Arrangements",
        quote: "A hybrid model, with a general expectation of 3 days in-office, is the standard for all teams.",
        tags: ["remote work", "work from home", "wfh", "policy", "hr"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
        documentId: "HR-POL-001",
        documentType: "policy",
        expertAuthor: "Sarah Chen - HR Director",
        lastUpdated: "2024-01-15",
        successRate: 95,
        relatedDocuments: ["HR-POL-002", "HR-POL-003"]
    },
    {
        id: 'AIK-02',
        question: "How do I submit an expense report?",
        answer: "Expense reports should be submitted through the 'AIT Finance Portal' within 15 days of the expense date. You must include itemized receipts for all purchases over $25. The portal can be accessed via the Quick Links in the company Wiki.",
        source: "Finance Policy: Travel and Expense Reimbursement",
        quote: "All expense reports must be submitted via the AIT Finance Portal. Receipts are required for expenditures exceeding $25.",
        tags: ["expense", "reimbursement", "finance", "travel", "receipts"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
        documentId: "FIN-POL-001",
        documentType: "policy",
        expertAuthor: "Michael Rodriguez - Finance Manager",
        lastUpdated: "2024-02-01",
        successRate: 88,
        relatedDocuments: ["FIN-POL-002", "FIN-FORM-001"]
    },
    {
        id: 'AIK-03',
        question: "What are the technical specifications for the VRV IV-S series?",
        answer: "The VRV IV-S series is a single-phase air-cooled outdoor unit designed for residential and light commercial use. It is available in 2, 3, and 4-ton capacities. Key features include Variable Refrigerant Temperature (VRT) for energy savings, a compact design, and a wide operation range down to -4°F for heating.",
        source: "Product Manual: VRV IV-S Series (RXSQ-TVJU)",
        quote: "Available in 2, 3, and 4-ton models, the single-phase VRV IV-S is notable for its VRT technology and compact casing.",
        tags: ["vrv", "technical", "specs", "specifications", "RXSQ", "product", "iv-s"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker']
    },
    {
        id: 'AIK-04',
        question: "What is Daikin's philosophy on sustainability and next-generation refrigerants?",
        answer: "Daikin is committed to reducing environmental impact through innovation. We are leading the global adoption of R-32, a refrigerant with a lower Global Warming Potential (GWP) compared to conventional refrigerants like R-410A. Our goal is to balance environmental concerns with performance and safety, providing sustainable solutions for the future.",
        source: "Knowledge Article: Next-Generation Refrigerant R-32",
        quote: "Daikin is committed to the adoption of R-32, a refrigerant with a significantly lower GWP than R-410A.",
        tags: ["sustainability", "environment", "refrigerant", "r-32", "r32", "gwp", "r&d"],
        accessRoles: ['CEO', 'Manager', 'QA Engineer', 'Worker', 'Super User', 'Engineer', 'Technician']
    },
    {
        id: 'AIK-05',
        question: "What should I do if a machine is in a 'Failure' state?",
        answer: "If a machine enters a 'Failure' state, you should immediately create a high-urgency 'Operations' ticket using the Smart Form. In the description, include the Machine ID (e.g., M-003) and the Fault Code displayed on the Maintenance Dashboard. Do not attempt to restart the machine without consulting an Operations Technician.",
        source: "Operations Policy: Machine Fault Protocol",
        quote: "A 'Failure' state requires the immediate creation of a high-urgency Operations ticket. Include Machine ID and Fault Code.",
        tags: ["machine", "failure", "operations", "maintenance", "ticket"],
        accessRoles: ['CEO', 'Manager', 'QA Engineer', 'Worker', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
        documentId: "OPS-POL-001",
        documentType: "policy",
        expertAuthor: "Bob Smith - Operations Manager",
        lastUpdated: "2024-03-01",
        successRate: 92,
        relatedDocuments: ["OPS-POL-002", "MAINT-GUIDE-001"]
    },
    {
        id: 'AIK-06',
        question: "How do I access the company Wiki?",
        answer: "The company Wiki can be accessed through the main navigation menu by clicking on 'Wiki' or through the Quick Links section. The Wiki contains knowledge articles, company announcements, and policy documents. All employees have read access, while content creation requires appropriate permissions based on your role.",
        source: "IT Guide: Internal Systems Access",
        quote: "The Wiki is accessible via the main navigation menu and contains knowledge articles, announcements, and policies.",
        tags: ["wiki", "access", "navigation", "knowledge", "documents"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
        documentId: "IT-GUIDE-001",
        documentType: "guide",
        expertAuthor: "David Lee - IT Manager",
        lastUpdated: "2024-02-15",
        successRate: 98,
        relatedDocuments: ["IT-GUIDE-002", "WIKI-HELP-001"]
    },
    {
        id: 'AIK-07',
        question: "What is the process for requesting new equipment?",
        answer: "Equipment requests should be submitted through the Smart Form system under 'Admin Request' category. Include detailed specifications, business justification, and budget information. Requests over $5,000 require manager approval, while requests over $25,000 need department head approval. Processing typically takes 5-10 business days.",
        source: "Admin Policy: Equipment Procurement",
        quote: "Equipment requests are processed through Smart Form with approval thresholds at $5,000 (manager) and $25,000 (department head).",
        tags: ["equipment", "request", "procurement", "approval", "admin"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
        documentId: "ADMIN-POL-001",
        documentType: "policy",
        expertAuthor: "Megan Green - HR Manager",
        lastUpdated: "2024-01-20",
        successRate: 89,
        relatedDocuments: ["ADMIN-POL-002", "FORM-GUIDE-001"]
    },
    {
        id: 'AIK-08',
        question: "How do I reset my password?",
        answer: "To reset your password, click on the 'Forgot Password' link on the login page, or contact IT Support at ext. 1234. For security reasons, password resets require identity verification. New passwords must be at least 8 characters long and include uppercase, lowercase, numbers, and special characters.",
        source: "IT Security: Password Management",
        quote: "Password resets require identity verification and must meet complexity requirements: 8+ characters with mixed case, numbers, and symbols.",
        tags: ["password", "reset", "security", "login", "it support"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
        documentId: "IT-SEC-001",
        documentType: "policy",
        expertAuthor: "David Lee - IT Manager",
        lastUpdated: "2024-02-28",
        successRate: 95,
        relatedDocuments: ["IT-SEC-002", "IT-HELP-001"]
    },
    {
        id: 'AIK-09',
        question: "What are the company's working hours and break policies?",
        answer: "Standard working hours are 8:00 AM to 5:00 PM, Monday through Friday, with a one-hour lunch break. Employees are entitled to two 15-minute breaks per day. Flexible start times between 7:00 AM and 9:00 AM are available with manager approval. Overtime requires prior authorization from your direct supervisor.",
        source: "HR Policy: Working Hours and Breaks",
        quote: "Standard hours are 8 AM-5 PM with flexible start times (7-9 AM) available with manager approval.",
        tags: ["working hours", "breaks", "schedule", "overtime", "policy"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
        documentId: "HR-POL-003",
        documentType: "policy",
        expertAuthor: "Megan Green - HR Manager",
        lastUpdated: "2024-01-10",
        successRate: 97,
        relatedDocuments: ["HR-POL-001", "HR-POL-004"]
    },
    {
        id: 'AIK-10',
        question: "How do I use the Smart Form system?",
        answer: "The Smart Form system is accessible from the main navigation menu. Select the appropriate form type (IT Support, Finance, Admin Request, etc.), fill in the required fields, and submit. The system will automatically route your request to the appropriate department. You can track the status of your submissions in the 'My Requests' section.",
        source: "User Guide: Smart Form System",
        quote: "Smart Forms automatically route requests to appropriate departments and allow status tracking through 'My Requests'.",
        tags: ["smart form", "requests", "system", "tracking", "navigation"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
        documentId: "FORM-GUIDE-001",
        documentType: "guide",
        expertAuthor: "System Administrator",
        lastUpdated: "2024-03-15",
        successRate: 94,
        relatedDocuments: ["FORM-GUIDE-002", "SYSTEM-HELP-001"]
    },
    {
        id: 'AIK-11',
        question: "What is the difference between VRV and VRF systems?",
        answer: "VRV (Variable Refrigerant Volume) and VRF (Variable Refrigerant Flow) are essentially the same technology. VRV is Daikin's trademarked term for this technology, while VRF is the generic industry term. Both systems use variable refrigerant flow to provide precise temperature control and energy efficiency by adjusting refrigerant flow to match the exact cooling or heating load required.",
        source: "Technical Manual: VRV/VRF Technology Overview",
        quote: "VRV is Daikin's trademarked term for Variable Refrigerant Volume technology, while VRF is the generic industry equivalent.",
        tags: ["vrv", "vrf", "technology", "refrigerant", "technical", "difference"],
        accessRoles: ['CEO', 'Manager', 'QA Engineer', 'Super User', 'Technician', 'Engineer'],
        documentId: "TECH-MAN-001",
        documentType: "manual",
        expertAuthor: "Sam Turquoise - R&D Engineer",
        lastUpdated: "2024-02-20",
        successRate: 91,
        relatedDocuments: ["TECH-MAN-002", "PROD-SPEC-001"]
    },
    {
        id: 'AIK-12',
        question: "How do I interpret fault codes on the maintenance dashboard?",
        answer: "Fault codes on the maintenance dashboard follow a standard format: [System]-[Component]-[Error Type]. For example, 'HVAC-COMP-01' indicates an HVAC compressor error type 01. Critical errors (red) require immediate attention, warnings (yellow) should be addressed within 24 hours, and informational codes (blue) are for monitoring purposes. Detailed fault code descriptions are available in the technical documentation.",
        source: "Maintenance Guide: Fault Code Interpretation",
        quote: "Fault codes follow format [System]-[Component]-[Error Type] with color-coded priority levels for response timing.",
        tags: ["fault codes", "maintenance", "dashboard", "errors", "troubleshooting"],
        accessRoles: ['CEO', 'Manager', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
        documentId: "MAINT-GUIDE-001",
        documentType: "guide",
        expertAuthor: "Uma Bronze - Operations Technician",
        lastUpdated: "2024-03-10",
        successRate: 88,
        relatedDocuments: ["MAINT-GUIDE-002", "TECH-REF-001"]
    },
    {
        id: 'AIK-13',
        question: "What are the safety protocols for working with refrigerants?",
        answer: "When working with refrigerants, always wear appropriate PPE including safety glasses, gloves, and protective clothing. Ensure adequate ventilation in work areas. R-32 refrigerant is mildly flammable, so avoid ignition sources. Use proper recovery equipment and never vent refrigerants to atmosphere. All refrigerant work must be performed by certified technicians following EPA regulations.",
        source: "Safety Manual: Refrigerant Handling Procedures",
        quote: "R-32 is mildly flammable requiring PPE, proper ventilation, and certified technician handling per EPA regulations.",
        tags: ["safety", "refrigerant", "r-32", "ppe", "epa", "certification"],
        accessRoles: ['CEO', 'Manager', 'QA Engineer', 'Super User', 'Technician', 'Engineer'],
        documentId: "SAFETY-MAN-001",
        documentType: "manual",
        expertAuthor: "Bob Smith - Operations Manager",
        lastUpdated: "2024-01-25",
        successRate: 96,
        relatedDocuments: ["SAFETY-MAN-002", "EPA-GUIDE-001"]
    },
    {
        id: 'AIK-14',
        question: "How do I access the predictive analytics dashboard?",
        answer: "The Predictive Analytics dashboard is available in the main navigation menu under 'Analytics'. You can choose between Individual Machine Analysis (for specific equipment) or System-Wide Analysis (for overall performance trends). The dashboard provides failure predictions, maintenance recommendations, and performance optimization insights based on machine learning algorithms.",
        source: "User Guide: Predictive Analytics Platform",
        quote: "Predictive Analytics offers Individual Machine and System-Wide analysis with ML-based failure predictions and maintenance recommendations.",
        tags: ["predictive analytics", "dashboard", "machine learning", "maintenance", "analysis"],
        accessRoles: ['CEO', 'Manager', 'QA Engineer', 'Super User', 'Technician', 'Engineer'],
        documentId: "ANALYTICS-GUIDE-001",
        documentType: "guide",
        expertAuthor: "Rachel Silver - R&D Manager",
        lastUpdated: "2024-03-20",
        successRate: 85,
        relatedDocuments: ["ANALYTICS-GUIDE-002", "ML-DOC-001"]
    },
    {
        id: 'AIK-15',
        question: "What is our company philosophy about people-centered management?",
        answer: "Our corporate philosophy is built upon the core principle of 'People-Centered Management.' We believe that the source of a company's competitiveness lies in its people. Our goal is to create an environment where each employee can work with enthusiasm and purpose, exercise their full potential, and achieve personal growth through empowerment, continuous learning, and open communication.",
        source: "Wiki Knowledge Article: Our Philosophy - People-Centered Management",
        quote: "We believe that the source of a company's competitiveness lies in its people.",
        tags: ["philosophy", "people-centered", "management", "culture", "values", "wiki"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
        documentId: "KA-01",
        documentType: "knowledge_article",
        expertAuthor: "Alice Johnson - Production",
        lastUpdated: "2024-01-15",
        successRate: 95,
        relatedDocuments: ["KA-04", "HR-POL-001"]
    },
    {
        id: 'AIK-16',
        question: "What is inverter technology and how does it work?",
        answer: "Inverter technology is a cornerstone of modern, energy-efficient HVAC systems. Unlike traditional fixed-speed systems that operate on a simple on/off cycle, an inverter-equipped system can continuously adjust its compressor speed. By varying the speed, the system can precisely match the cooling or heating load required at any given moment, resulting in significant energy savings and superior comfort.",
        source: "Wiki Knowledge Article: Introduction to Inverter Technology",
        quote: "Inverter technology allows continuous adjustment of compressor speed to match exact cooling or heating requirements.",
        tags: ["inverter", "technology", "hvac", "energy", "efficiency", "compressor", "wiki"],
        accessRoles: ['CEO', 'Manager', 'QA Engineer', 'Super User', 'Technician', 'Engineer'],
        documentId: "KA-02",
        documentType: "knowledge_article",
        expertAuthor: "Sam Turquoise - R&D",
        lastUpdated: "2024-05-20",
        successRate: 93,
        relatedDocuments: ["KA-03", "TECH-MAN-001"]
    },
    {
        id: 'AIK-17',
        question: "What is the Fusion 25 management plan?",
        answer: "Fusion 25 is our strategic management plan designed to guide Daikin through 2025. It focuses on achieving both business growth and a significant contribution to a sustainable society. Key pillars include: Challenge to Achieve Carbon Neutrality, Creation of Value with Air, and Enhancing the Value of People-Centered Management.",
        source: "Wiki Knowledge Article: The Fusion 25 Management Plan",
        quote: "Fusion 25 focuses on achieving both business growth and a significant contribution to a sustainable society.",
        tags: ["fusion 25", "strategy", "management", "plan", "sustainability", "growth", "wiki"],
        accessRoles: ['CEO', 'Manager', 'QA Engineer', 'Super User', 'Engineer'],
        documentId: "KA-04",
        documentType: "knowledge_article",
        expertAuthor: "Alice Johnson - Production",
        lastUpdated: "2024-02-10",
        successRate: 91,
        relatedDocuments: ["KA-01", "STRATEGY-DOC-001"]
    },
    {
        id: 'AIK-18',
        question: "What are the latest company announcements?",
        answer: "Recent company announcements include the launch of our new global brand campaign 'Perfecting the Air', Q3 financial results showing strong performance in North America and Southeast Asia, and the annual employee engagement survey which is currently live until August 20th.",
        source: "Wiki Announcements: Recent Company News",
        quote: "Recent announcements include the 'Perfecting the Air' campaign launch and strong Q3 financial results.",
        tags: ["announcements", "news", "campaign", "financial", "results", "survey", "wiki"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
        documentId: "AN-01",
        documentType: "announcement",
        expertAuthor: "Sarah Chen - Marketing",
        lastUpdated: "2024-08-05",
        successRate: 88,
        relatedDocuments: ["AN-02", "AN-03"]
    },
    {
        id: 'AIK-06',
        question: "Who is the manager of the IT department?",
        answer: "The Manager of the IT department is David Lee. You can find more information about team structures in the Organization Chart.",
        source: "Employee Directory",
        quote: "David Lee, Role: Manager, Department: IT",
        tags: ["manager", "it", "department", "org chart", "who is"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker']
    },
    {
        id: 'AIK-07',
        question: "How do I reset my password?",
        answer: "To reset your password, contact the IT Support team through the Smart Form system or call the IT helpdesk at extension x5512. For security reasons, password resets require identity verification and must be done by authorized IT personnel.",
        source: "IT Security Policy: Password Management",
        quote: "Password resets require identity verification and must be performed by authorized IT personnel only.",
        tags: ["password", "reset", "it support", "security", "login", "access"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker']
    },
    {
        id: 'AIK-08',
        question: "What are the operating hours for the factory?",
        answer: "The main production facility operates 24/7 with three shifts: Day shift (6:00 AM - 2:00 PM), Evening shift (2:00 PM - 10:00 PM), and Night shift (10:00 PM - 6:00 AM). Administrative offices operate Monday through Friday, 8:00 AM to 5:00 PM.",
        source: "Operations Manual: Facility Hours",
        quote: "Production operates 24/7 in three 8-hour shifts. Administrative hours are Monday-Friday, 8:00 AM to 5:00 PM.",
        tags: ["hours", "factory", "production", "shifts", "schedule", "operations"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker']
    },
    {
        id: 'AIK-09',
        question: "What is the procedure for reporting safety incidents?",
        answer: "All safety incidents must be reported immediately to your supervisor and the Safety Officer. Use the 'Safety Incident Report' form in the Smart Form system within 24 hours. For emergencies, call 911 first, then notify the Safety Officer at extension x3333.",
        source: "Safety Policy: Incident Reporting Procedures",
        quote: "Safety incidents require immediate supervisor notification and formal reporting within 24 hours using the designated form.",
        tags: ["safety", "incident", "report", "emergency", "procedure", "supervisor"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker']
    },
    {
        id: 'AIK-10',
        question: "What are the benefits of VRV technology?",
        answer: "Variable Refrigerant Volume (VRV) technology offers superior energy efficiency, precise temperature control, and flexible system design. It allows individual zone control, reduces energy consumption by up to 30% compared to traditional systems, and provides quiet operation with minimal maintenance requirements.",
        source: "Technical Guide: VRV Technology Overview",
        quote: "VRV technology delivers up to 30% energy savings with precise zone control and minimal maintenance requirements.",
        tags: ["vrv", "technology", "benefits", "energy", "efficiency", "zone control", "technical"],
        accessRoles: ['CEO', 'Manager', 'QA Engineer', 'Super User', 'Engineer', 'Technician']
    },
    {
        id: 'AIK-11',
        question: "How do I request vacation time?",
        answer: "Vacation requests should be submitted through the HR portal at least 2 weeks in advance. Requests require manager approval and are subject to operational needs. You accrue vacation time based on your length of service: 2 weeks for 0-2 years, 3 weeks for 3-7 years, and 4 weeks for 8+ years.",
        source: "HR Policy: Time Off and Vacation",
        quote: "Vacation requests require 2 weeks advance notice and manager approval. Accrual rates vary by service length.",
        tags: ["vacation", "time off", "hr", "request", "approval", "accrual"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker']
    },
    {
        id: 'AIK-12',
        question: "What is the company's quality assurance process?",
        answer: "Our QA process follows ISO 9001 standards with multiple inspection points throughout production. Each unit undergoes functional testing, performance verification, and final inspection before shipment. Quality metrics are tracked in real-time through our digital dashboard system.",
        source: "Quality Manual: QA Process Overview",
        quote: "ISO 9001 compliant process with multiple inspection points and real-time quality metric tracking.",
        tags: ["quality", "qa", "iso", "testing", "inspection", "standards", "process"],
        accessRoles: ['CEO', 'Manager', 'QA Engineer', 'Super User', 'Engineer', 'Production Worker']
    },
    {
        id: 'AIK-13',
        question: "What training is required for new employees?",
        answer: "All new employees must complete a comprehensive onboarding program including safety training, company policies orientation, and role-specific technical training. The program typically takes 2-3 weeks and includes both online modules and hands-on sessions with experienced team members.",
        source: "HR Manual: Employee Onboarding",
        quote: "Comprehensive 2-3 week onboarding includes safety training, policy orientation, and role-specific technical training.",
        tags: ["training", "onboarding", "new employee", "safety", "orientation", "hr"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker']
    },
    {
        id: 'AIK-14',
        question: "How do I access the company VPN?",
        answer: "VPN access is provided to authorized employees for remote work. Contact IT Support to request VPN credentials and download the approved VPN client. You'll need to complete a security training module before access is granted. VPN usage is monitored and logged for security purposes.",
        source: "IT Security Policy: VPN Access",
        quote: "VPN access requires IT approval, security training completion, and is subject to monitoring for security compliance.",
        tags: ["vpn", "remote access", "security", "it", "credentials", "monitoring"],
        accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer']
    },
    {
        id: 'AIK-15',
        question: "What is our environmental sustainability policy?",
        answer: "AIT Systems is committed to environmental sustainability through energy-efficient product design, waste reduction, and responsible manufacturing practices. We aim to achieve carbon neutrality by 2030 through renewable energy adoption, supply chain optimization, and innovative refrigerant technologies like R-32.",
        source: "Corporate Policy: Environmental Sustainability",
        quote: "Committed to carbon neutrality by 2030 through energy efficiency, waste reduction, and innovative refrigerant technologies.",
        tags: ["sustainability", "environment", "carbon neutral", "renewable energy", "r-32", "policy"],
        accessRoles: ['CEO', 'Manager', 'QA Engineer', 'Super User', 'Engineer']
    },
    {
        id: 'AIK-16',
        question: "What are the company's financial performance metrics for Q3?",
        answer: "Q3 financial performance shows 15% revenue growth compared to Q2, with EBITDA margin improving to 22%. Key drivers include increased demand for VRV systems and successful cost optimization initiatives. Net profit increased by 18% year-over-year.",
        source: "Financial Report: Q3 Performance Summary",
        quote: "Q3 revenue growth of 15% with EBITDA margin at 22% and 18% net profit increase year-over-year.",
        tags: ["financial", "performance", "q3", "revenue", "profit", "ebitda", "metrics"],
        accessRoles: ['CEO', 'Manager', 'Super User']
    },
    {
        id: 'AIK-17',
        question: "What are the salary ranges for different positions?",
        answer: "Salary ranges vary by position and experience: Engineers (RM 4,500-8,000), Managers (RM 8,000-15,000), Technicians (RM 3,000-5,500), Production Workers (RM 2,500-4,000). These ranges include base salary and may be supplemented by performance bonuses.",
        source: "HR Confidential: Compensation Structure",
        quote: "Position-based salary ranges from RM 2,500 for entry-level to RM 15,000 for senior management roles.",
        tags: ["salary", "compensation", "ranges", "hr", "confidential", "positions"],
        accessRoles: ['CEO', 'Super User']
    },
    {
        id: 'AIK-18',
        question: "What is the company's strategic expansion plan?",
        answer: "The 5-year strategic plan includes expansion into Southeast Asian markets, with new facilities planned in Vietnam and Thailand by 2026. Investment of RM 50 million allocated for R&D and manufacturing capacity expansion. Target market share increase from 15% to 25% in the region.",
        source: "Strategic Planning: 5-Year Expansion Roadmap",
        quote: "RM 50 million investment for Southeast Asian expansion with facilities in Vietnam and Thailand by 2026.",
        tags: ["strategic", "expansion", "plan", "investment", "southeast asia", "facilities"],
        accessRoles: ['CEO', 'Super User']
    }
];