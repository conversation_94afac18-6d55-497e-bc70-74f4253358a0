# 🚀 Daikin AI System - Scripts Usage Guide

## 📋 Scripts Overview

This project provides a complete set of automation scripts to help you quickly start, test, and demonstrate the Daikin AI System.

### 🎯 Main Scripts

| Script Name | Platform | Function Description |
|---------|------|----------|
| `main-menu.bat` | Windows | Main console, unified entry for all functions |
| `start-system.bat` | Windows | One-click system startup (check environment, start services) |
| `start-system.sh` | Linux/Mac | One-click system startup (Unix version) |
| `check-system.bat` | Windows | System status check and diagnostics |
| `run-tests.bat` | Windows | Automated test suite |
| `run-tests.sh` | Linux/Mac | Automated test suite (Unix version) |
| `quick-demo.bat` | Windows | 5-minute quick demo guide |

---

## 🚀 Quick Start

### Windows Users

1. **Start Main Console**
   ```cmd
   main-menu.bat
   ```

2. **Or Direct System Startup**
   ```cmd
   start-system.bat
   ```

### Linux/Mac Users

1. **Add Execute Permissions**
   ```bash
   chmod +x *.sh
   ```

2. **Start System**
   ```bash
   ./start-system.sh
   ```

---

## 📖 Detailed Usage Instructions

### 1. Main Console (`main-menu.bat`)

This is the main system entry point, providing the following functions:

- **System Status Check** - Check all dependencies and service status
- **One-Click System Start** - Automatically start all required services
- **Quick Demo** - 5-minute demo guide
- **Complete Testing** - Run all test suites
- **View Guide** - Open test documentation
- **System Maintenance** - Clear cache, reinstall dependencies, etc.
- **System Information** - View system configuration info

### 2. System Startup Scripts

#### Windows: `start-system.bat`
#### Linux/Mac: `start-system.sh`

**Functions:**
- Check Node.js and npm installation
- Check and start Ollama service
- Verify AI model availability
- Install project dependencies
- Start development server
- Automatically open browser

### 3. System Check Script (`check-system.bat`)

**Check Items:**
- Node.js version
- npm availability
- Project dependency installation status
- Ollama service status
- AI model installation status
- Development server status
- Port usage
- System resource usage

### 4. Test Scripts

#### Windows: `run-tests.bat`
#### Linux/Mac: `run-tests.sh`

**Test Content:**
- Basic connection test
- AI service test
- API endpoint test
- Performance benchmark test
- System resource check

### 5. Quick Demo Script (`quick-demo.bat`)

**Demo Flow:**
1. System homepage display (30 seconds)
2. AI Assistant functionality (90 seconds)
3. Smart Forms demo (90 seconds)
4. Maintenance Dashboard (90 seconds)
5. Predictive Analytics display (90 seconds)

**Total Duration:** 5 minutes

---

## 🔧 System Requirements

### Basic Requirements
- **Node.js**: v16.0.0 or higher
- **Memory**: Minimum 4GB, recommended 8GB
- **Storage**: At least 2GB available space
- **Network**: Stable connection (for downloading AI models)

### Dependent Services
- **Ollama**: AI inference service
- **llama3.1:8b**: AI model
- **Modern Browser**: Chrome, Edge, Firefox

---

## 🛠️ Troubleshooting

### Common Issues

#### 1. Node.js Not Installed
```bash
# Error message
❌ Error: Node.js not found

# Solution
# Visit https://nodejs.org to download and install
```

#### 2. Ollama Service Not Running
```bash
# Error message
❌ Ollama service not running

# Solution
ollama serve
```

#### 3. AI Model Not Installed
```bash
# Error message
❌ llama3.1:8b model not installed

# Solution
ollama pull llama3.1:8b
```

#### 4. Port Conflict
```bash
# Error message
Port 5173 already in use

# Solution (Windows)
netstat -ano | findstr :5173
taskkill /PID <PID> /F

# Solution (Linux/Mac)
lsof -i :5173
kill -9 <PID>
```

#### 5. Dependency Installation Failed
```bash
# Solution
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

---

## 📊 Test Reports

### Auto-Generated Reports
Test scripts automatically generate detailed reports:

```
test-results/
├── test-report-2024-01-15_14-30-25.txt
├── test-report-2024-01-15_15-45-10.txt
└── ...
```

### Report Content
- Test execution time
- Individual test results
- Performance metrics
- System resource usage
- Error details

---

## 🎯 Demo Scenarios

### Scenario 1: Emergency Breakdown Response (8 minutes)
- **Goal**: Prove 15-minute resolution vs 72-hour traditional method
- **Savings**: RM70,000 per incident

### Scenario 2: Equipment Purchase Workflow (9 minutes)
- **Goal**: Prove 3-day approval vs 21-day traditional process
- **Efficiency**: 85% time savings

### Scenario 3: Predictive Maintenance (8 minutes)
- **Goal**: Prove RM70K savings per prediction
- **ROI**: 467% return on investment

---

## 📚 Related Documentation

| Document Name | Description |
|---------|------|
| `TESTING_GUIDE.md` | Complete English testing guide |
| `COMPLETE_TEST_GUIDE.md` | Detailed demo scenarios |
| `SETUP_GUIDE.md` | System configuration guide |
| `README.md` | Basic project description |

---

## 💡 Best Practices

### Demo Preparation
1. Run system check to ensure all components are normal
2. Execute quick demo to familiarize with process
3. Prepare backup plans for network issues
4. Pre-test all function modules

### Test Execution
1. Regularly run automated tests
2. Save test reports for analysis
3. Monitor system performance metrics
4. Address discovered issues promptly

### System Maintenance
1. Regularly clean cache and temporary files
2. Keep dependency packages updated
3. Monitor system resource usage
4. Backup important configurations and data

---

**Remember**: The goal of these scripts is to simplify system management and demo processes, ensuring each demonstration can smoothly showcase the RM450K annual savings value from a RM240K investment.
