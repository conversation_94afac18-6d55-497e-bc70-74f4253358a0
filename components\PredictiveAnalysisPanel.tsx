/**
 * Predictive Analysis Panel
 * Displays machine failure predictions and expert recommendations
 */

import React, { useState, useContext } from 'react';
import { AppContext } from './AppContext';
import { analyzeSystemPatterns, type PredictiveAnalysis } from '../services/predictiveAnalyticsService';
import {
    ExclamationTriangleIcon,
    CheckCircleIcon,
    ClockIcon,
    UserIcon,
    ChartBarIcon,
    WrenchScrewdriverIcon,
    BoltIcon,
    CpuChipIcon
} from './icons/Icons';


const PredictiveAnalysisPanel: React.FC = () => {
    const { machines, tickets, alerts, currentUser } = useContext(AppContext);
    const [selectedMachine, setSelectedMachine] = useState<string>('');
    const [analysis, setAnalysis] = useState<PredictiveAnalysis | null>(null);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [systemAnalysis, setSystemAnalysis] = useState<any>(null);
    const [error, setError] = useState<string | null>(null);
    const [analysisMode, setAnalysisMode] = useState<'system' | 'individual'>('individual');



    const handleSystemAnalysis = async () => {
        setIsAnalyzing(true);
        try {
            const result = await analyzeSystemPatterns(machines, tickets, alerts);
            setSystemAnalysis(result);
        } catch (error) {
            console.error('System analysis failed:', error);
        } finally {
            setIsAnalyzing(false);
        }
    };

    // Simplified analysis function that doesn't rely on complex service
    const handleSimpleAnalysis = () => {
        console.log('🔧 Running simplified analysis...');
        setError(null);
        setIsAnalyzing(true);

        // Simulate analysis delay
        setTimeout(() => {
            try {
                const machine = machines.find(m => m.id === selectedMachine);
                if (!machine) {
                    setError('Machine not found');
                    setIsAnalyzing(false);
                    return;
                }

                // Generate analysis based on machine status
                let failureProbability = 0.3; // Base probability
                let predictionType: PredictiveAnalysis['prediction_type'] = 'pattern_analysis';

                if (machine.status === 'Failure') {
                    failureProbability = 0.85;
                    predictionType = 'failure_prediction';
                } else if (machine.status === 'Warning') {
                    failureProbability = 0.65;
                    predictionType = 'maintenance_recommendation';
                }

                // Add health score factor
                if (machine.healthScore < 50) failureProbability += 0.2;
                else if (machine.healthScore < 70) failureProbability += 0.1;

                failureProbability = Math.min(failureProbability, 0.95);

                const daysUntilFailure = Math.max(1, Math.floor((1 - failureProbability) * 90));
                const predictedDate = new Date();
                predictedDate.setDate(predictedDate.getDate() + daysUntilFailure);

                const result: PredictiveAnalysis = {
                    prediction_type: predictionType,
                    confidence_score: 0.75 + Math.random() * 0.2,
                    predicted_failure_date: predictedDate.toISOString().split('T')[0],
                    failure_probability: failureProbability,
                    historical_patterns: [],
                    recommended_actions: [{
                        action_id: 'MAINTENANCE_ACTION',
                        description: `${machine.status === 'Failure' ? 'Immediate repair' : 'Preventive maintenance'} for ${machine.name}`,
                        priority: machine.status === 'Failure' ? 'critical' : machine.status === 'Warning' ? 'high' : 'medium',
                        estimated_time: machine.status === 'Failure' ? '4-8 hours' : '2-4 hours',
                        success_probability: 90,
                        required_expertise: ['Technician', 'Specialist']
                    }],
                    expert_insights: [],
                    similar_cases: []
                };

                setAnalysis(result);
                console.log('✅ Simplified analysis completed:', result);
            } catch (error) {
                console.error('❌ Simplified analysis failed:', error);
                setError(`Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            } finally {
                setIsAnalyzing(false);
            }
        }, 1500);
    };



    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'critical': return 'text-red-600 bg-red-50 border-red-200';
            case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
            case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
            case 'low': return 'text-green-600 bg-green-50 border-green-200';
            default: return 'text-gray-600 bg-gray-50 border-gray-200';
        }
    };

    const getFailureProbabilityColor = (probability: number) => {
        if (probability >= 0.7) return 'text-red-600';
        if (probability >= 0.4) return 'text-orange-600';
        if (probability >= 0.2) return 'text-yellow-600';
        return 'text-green-600';
    };

    return (
        <div className="p-6 space-y-6">
            {/* Header */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">Predictive Analytics</h2>
                    <p className="text-gray-500">AI-powered failure prediction and maintenance recommendations</p>
                </div>
            </div>

            {/* Two Analysis Options */}
            {/* Unified Analysis Panel */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
                {/* Analysis Mode Toggle */}
                <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">Predictive Analysis</h3>
                    <div className="flex bg-gray-100 rounded-lg p-1 border border-gray-200">
                        <button
                            onClick={() => {
                                setAnalysisMode('individual');
                                setError(null);
                                setAnalysis(null);
                                setSystemAnalysis(null);
                            }}
                            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                                analysisMode === 'individual'
                                    ? 'bg-white text-blue-600 shadow-sm border border-blue-200'
                                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                            }`}
                        >
                            <div className="flex items-center gap-2">
                                <CpuChipIcon className="w-4 h-4" />
                                <span>Individual Machine</span>
                            </div>
                        </button>
                        <button
                            onClick={() => {
                                setAnalysisMode('system');
                                setError(null);
                                setAnalysis(null);
                                setSystemAnalysis(null);
                            }}
                            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                                analysisMode === 'system'
                                    ? 'bg-white text-green-600 shadow-sm border border-green-200'
                                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                            }`}
                        >
                            <div className="flex items-center gap-2">
                                <ChartBarIcon className="w-4 h-4" />
                                <span>System-Wide</span>
                            </div>
                        </button>
                    </div>
                </div>

                {/* Analysis Content */}
                <div className="space-y-4">
                    {analysisMode === 'individual' ? (
                        <>
                            {/* Individual Machine Analysis */}
                            <div className="bg-blue-50 rounded-lg p-4">
                                <h4 className="font-medium text-blue-900 mb-2">Individual Machine Analysis</h4>
                                <p className="text-sm text-blue-700">Get detailed predictions and maintenance recommendations for a specific machine</p>
                            </div>

                            {/* Debug Info */}
                            <div className="p-3 bg-gray-50 rounded-lg text-xs">
                                <div>Available machines: {machines.length}</div>
                                <div>Selected: {selectedMachine || 'None'}</div>
                                <div>Status: {isAnalyzing ? 'Analyzing' : 'Ready'}</div>
                            </div>

                            <div className="space-y-3">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Select Machine</label>
                                    <select
                                        value={selectedMachine}
                                        onChange={(e) => {
                                            console.log('Machine selected:', e.target.value);
                                            setSelectedMachine(e.target.value);
                                            setError(null);
                                        }}
                                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="">Choose a machine...</option>
                                        {machines.map(machine => (
                                            <option key={machine.id} value={machine.id}>
                                                {machine.name} - {machine.machineType} ({machine.status})
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <button
                                    onClick={() => {
                                        console.log('🔍 Individual analysis button clicked!');
                                        handleSimpleAnalysis();
                                    }}
                                    disabled={!selectedMachine || isAnalyzing}
                                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center gap-2 transition-all font-medium"
                                >
                                    {isAnalyzing ? (
                                        <>
                                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                                            <span>Analyzing Machine...</span>
                                        </>
                                    ) : (
                                        <>
                                            <CpuChipIcon className="w-5 h-5" />
                                            <span>Analyze Machine</span>
                                        </>
                                    )}
                                </button>
                            </div>
                        </>
                    ) : (
                        <>
                            {/* System-Wide Analysis */}
                            <div className="bg-green-50 rounded-lg p-4">
                                <h4 className="font-medium text-green-900 mb-2">System-Wide Analysis</h4>
                                <p className="text-sm text-green-700">Analyze overall system health, patterns, and cross-machine correlations</p>
                            </div>

                            <button
                                onClick={() => {
                                    console.log('🔍 System analysis button clicked!');
                                    handleSystemAnalysis();
                                }}
                                disabled={isAnalyzing}
                                className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center gap-2 transition-all font-medium"
                            >
                                {isAnalyzing ? (
                                    <>
                                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                                        <span>Analyzing System...</span>
                                    </>
                                ) : (
                                    <>
                                        <ChartBarIcon className="w-5 h-5" />
                                        <span>Run System Analysis</span>
                                    </>
                                )}
                            </button>
                        </>
                    )}

                    {/* Error Display */}
                    {error && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                            <div className="flex items-center gap-2">
                                <ExclamationTriangleIcon className="w-4 h-4 text-red-500" />
                                <span className="text-sm text-red-700">{error}</span>
                            </div>
                        </div>
                    )}

                    {/* Success Indicator */}
                    {(analysis || systemAnalysis) && !error && (
                        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                            <div className="flex items-center gap-2">
                                <CheckCircleIcon className="w-4 h-4 text-green-500" />
                                <span className="text-sm text-green-700">
                                    {analysisMode === 'individual' ? 'Machine analysis completed successfully!' : 'System analysis completed successfully!'}
                                </span>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Analysis Results */}
            {systemAnalysis && analysisMode === 'system' && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <div className="flex items-center gap-2 mb-4">
                        <ChartBarIcon className="w-5 h-5 text-green-600" />
                        <h3 className="text-lg font-semibold text-gray-900">System-Wide Analysis Results</h3>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div className="bg-blue-50 rounded-lg p-4">
                            <div className="flex items-center gap-2 mb-2">
                                <ChartBarIcon className="w-5 h-5 text-blue-600" />
                                <span className="font-medium text-gray-900">Health Score</span>
                            </div>
                            <div className="text-2xl font-bold text-blue-600">
                                {systemAnalysis.system_health_score}%
                            </div>
                        </div>
                        
                        <div className="bg-red-50 rounded-lg p-4">
                            <div className="flex items-center gap-2 mb-2">
                                <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />
                                <span className="font-medium text-gray-900">High Risk</span>
                            </div>
                            <div className="text-2xl font-bold text-red-600">
                                {systemAnalysis.risk_assessment.high_risk_machines.length}
                            </div>
                        </div>
                        
                        <div className="bg-yellow-50 rounded-lg p-4">
                            <div className="flex items-center gap-2 mb-2">
                                <ClockIcon className="w-5 h-5 text-yellow-600" />
                                <span className="font-medium text-gray-900">Medium Risk</span>
                            </div>
                            <div className="text-2xl font-bold text-yellow-600">
                                {systemAnalysis.risk_assessment.medium_risk_machines.length}
                            </div>
                        </div>
                        
                        <div className="bg-green-50 rounded-lg p-4">
                            <div className="flex items-center gap-2 mb-2">
                                <CheckCircleIcon className="w-5 h-5 text-green-600" />
                                <span className="font-medium text-gray-900">Low Risk</span>
                            </div>
                            <div className="text-2xl font-bold text-green-600">
                                {systemAnalysis.risk_assessment.low_risk_machines.length}
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 className="font-semibold text-gray-900 mb-3">Trending Issues</h4>
                            <div className="space-y-2">
                                {systemAnalysis.trending_issues.map((issue: string, index: number) => (
                                    <div key={index} className="flex items-center gap-2 text-sm">
                                        <BoltIcon className="w-4 h-4 text-orange-500" />
                                        <span>{issue}</span>
                                    </div>
                                ))}
                            </div>
                        </div>
                        
                        <div>
                            <h4 className="font-semibold text-gray-900 mb-3">Recommendations</h4>
                            <div className="space-y-2">
                                {systemAnalysis.recommendations.map((rec: string, index: number) => (
                                    <div key={index} className="flex items-center gap-2 text-sm">
                                        <CheckCircleIcon className="w-4 h-4 text-green-500" />
                                        <span>{rec}</span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Individual Machine Analysis Results */}
            {analysis && analysisMode === 'individual' && (
                <div className="space-y-6">
                    {/* Prediction Summary */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center gap-2 mb-4">
                            <CpuChipIcon className="w-5 h-5 text-blue-600" />
                            <h3 className="text-lg font-semibold text-gray-900">Individual Machine Analysis Results</h3>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div className="bg-gray-50 rounded-lg p-4">
                                <div className="text-sm text-gray-600 mb-1">Prediction Type</div>
                                <div className="font-semibold capitalize">{analysis.prediction_type.replace('_', ' ')}</div>
                            </div>
                            
                            <div className="bg-gray-50 rounded-lg p-4">
                                <div className="text-sm text-gray-600 mb-1">Failure Probability</div>
                                <div className={`font-semibold ${getFailureProbabilityColor(analysis.failure_probability)}`}>
                                    {(analysis.failure_probability * 100).toFixed(0)}%
                                </div>
                            </div>
                            
                            <div className="bg-gray-50 rounded-lg p-4">
                                <div className="text-sm text-gray-600 mb-1">Confidence Score</div>
                                <div className="font-semibold text-blue-600">
                                    {(analysis.confidence_score * 100).toFixed(0)}%
                                </div>
                            </div>
                        </div>

                        {analysis.predicted_failure_date && (
                            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                                <div className="flex items-center gap-2 mb-2">
                                    <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />
                                    <span className="font-semibold text-red-900">Predicted Failure Date</span>
                                </div>
                                <div className="text-red-800">
                                    {new Date(analysis.predicted_failure_date).toLocaleDateString()}
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Recommended Actions */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recommended Actions</h3>
                        <div className="space-y-4">
                            {analysis.recommended_actions.map((action, index) => (
                                <div key={action.action_id} className={`border rounded-lg p-4 ${getPriorityColor(action.priority)}`}>
                                    <div className="flex items-start justify-between mb-2">
                                        <div className="flex items-center gap-2">
                                            <WrenchScrewdriverIcon className="w-5 h-5" />
                                            <span className="font-semibold">{action.description}</span>
                                        </div>
                                        <span className={`px-2 py-1 rounded text-xs font-medium capitalize ${getPriorityColor(action.priority)}`}>
                                            {action.priority}
                                        </span>
                                    </div>
                                    
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                        <div>
                                            <span className="text-gray-600">Time: </span>
                                            <span className="font-medium">{action.estimated_time}</span>
                                        </div>
                                        <div>
                                            <span className="text-gray-600">Success Rate: </span>
                                            <span className="font-medium">{action.success_probability}%</span>
                                        </div>
                                        <div>
                                            <span className="text-gray-600">Expertise: </span>
                                            <span className="font-medium">{action.required_expertise.join(', ')}</span>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Expert Insights */}
                    {analysis.expert_insights.length > 0 && (
                        <div className="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Expert Insights</h3>
                            <div className="space-y-4">
                                {analysis.expert_insights.map((expert, index) => (
                                    <div key={index} className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <div className="flex items-start gap-3">
                                            <UserIcon className="w-5 h-5 text-blue-600 mt-1" />
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between mb-2">
                                                    <span className="font-semibold text-blue-900">{expert.expert_name}</span>
                                                    <span className="text-sm text-blue-600">
                                                        Confidence: {expert.confidence_level}%
                                                    </span>
                                                </div>
                                                <p className="text-blue-800 mb-2">{expert.insight}</p>
                                                <div className="text-sm text-blue-600">
                                                    <span className="font-medium">Expertise: </span>
                                                    {expert.expertise_areas.join(', ')}
                                                </div>
                                                <div className="text-sm text-blue-600 mt-1">
                                                    <span className="font-medium">Contact: </span>
                                                    {expert.contact_info}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Similar Cases */}
                    {analysis.similar_cases.length > 0 && (
                        <div className="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Similar Historical Cases</h3>
                            <div className="space-y-4">
                                {analysis.similar_cases.map((case_, index) => (
                                    <div key={case_.case_id} className="bg-green-50 border border-green-200 rounded-lg p-4">
                                        <div className="flex items-start justify-between mb-2">
                                            <span className="font-semibold text-green-900">{case_.case_id}</span>
                                            <span className="text-sm text-green-600">
                                                Success Rate: {case_.success_rate}%
                                            </span>
                                        </div>
                                        
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span className="font-medium text-green-800">Symptoms: </span>
                                                <span className="text-green-700">{case_.symptoms.join(', ')}</span>
                                            </div>
                                            <div>
                                                <span className="font-medium text-green-800">Resolution: </span>
                                                <span className="text-green-700">{case_.resolution}</span>
                                            </div>
                                            <div>
                                                <span className="font-medium text-green-800">Time: </span>
                                                <span className="text-green-700">{case_.resolution_time}</span>
                                            </div>
                                        </div>
                                        
                                        <div className="mt-2 text-sm">
                                            <span className="font-medium text-green-800">Lessons Learned: </span>
                                            <span className="text-green-700">{case_.lessons_learned}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}


                </div>
            )}
        </div>
    );
};

export default PredictiveAnalysisPanel;
