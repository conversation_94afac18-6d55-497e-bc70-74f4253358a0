#!/bin/bash

# Set colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "========================================"
echo "🧪 Daikin AI System - Automated Test Suite"
echo "========================================"
echo -e "${NC}"

# Switch to project root directory
cd "$(dirname "$0")/.."

# Check if system is running
echo -e "${BLUE}📋 Checking system status...${NC}"
if ! curl -s http://localhost:5173 > /dev/null 2>&1; then
    echo -e "${RED}❌ System not running, please start system first${NC}"
    echo "Run: ./start-system.sh"
    exit 1
fi

echo -e "${GREEN}✅ System is running${NC}"

# Check Ollama service
if ! curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo -e "${RED}❌ Ollama service not running${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Ollama service normal${NC}"

echo ""
echo -e "${BLUE}🚀 Starting test suite execution...${NC}"
echo ""

# Create test results directory
mkdir -p test-results

# Get current timestamp
timestamp=$(date +"%Y-%m-%d_%H-%M-%S")

echo -e "${BLUE}📊 Test report will be saved to: test-results/test-report-${timestamp}.txt${NC}"

# Start testing
echo "Test start time: $(date)" > test-results/test-report-${timestamp}.txt
echo "========================================" >> test-results/test-report-${timestamp}.txt

# Test 1: Basic connection test
echo ""
echo -e "${BLUE}🔗 Test 1: Basic Connection Test${NC}"
echo "[TEST 1] Basic Connection Test" >> test-results/test-report-${timestamp}.txt

response=$(curl -s -o /dev/null -w "HTTP Status: %{http_code}, Response Time: %{time_total}s" http://localhost:5173)
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Passed${NC}"
    echo "✅ Passed - $response" >> test-results/test-report-${timestamp}.txt
else
    echo -e "${RED}❌ Failed${NC}"
    echo "❌ Failed" >> test-results/test-report-${timestamp}.txt
fi

# Test 2: AI service test
echo ""
echo -e "${BLUE}🤖 Test 2: AI Service Connection Test${NC}"
echo "[TEST 2] AI Service Connection Test" >> test-results/test-report-${timestamp}.txt

if curl -s -X POST http://localhost:11434/api/generate \
   -d '{"model":"llama3.1:8b","prompt":"Hello","stream":false}' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Passed${NC}"
    echo "✅ Passed" >> test-results/test-report-${timestamp}.txt
else
    echo -e "${RED}❌ Failed${NC}"
    echo "❌ Failed" >> test-results/test-report-${timestamp}.txt
fi

# Test 3: API endpoint test
echo ""
echo -e "${BLUE}📡 Test 3: API Endpoint Test${NC}"
echo "[TEST 3] API Endpoint Test" >> test-results/test-report-${timestamp}.txt

pages=("/" "/dashboard")
for page in "${pages[@]}"; do
    response=$(curl -s -o /dev/null -w "HTTP: %{http_code}" http://localhost:5173${page})
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Page ${page} normal${NC}"
        echo "✅ Page ${page} normal - $response" >> test-results/test-report-${timestamp}.txt
    else
        echo -e "${RED}❌ Page ${page} failed${NC}"
        echo "❌ Page ${page} failed" >> test-results/test-report-${timestamp}.txt
    fi
done

# Test 4: Performance test
echo ""
echo -e "${BLUE}⚡ Test 4: Performance Benchmark Test${NC}"
echo "[TEST 4] Performance Benchmark Test" >> test-results/test-report-${timestamp}.txt

total_time=0
for i in {1..5}; do
    response_time=$(curl -s -o /dev/null -w "%{time_total}" http://localhost:5173)
    echo "Request $i - Response Time: ${response_time}s"
    echo "Request $i - Response Time: ${response_time}s" >> test-results/test-report-${timestamp}.txt
    total_time=$(echo "$total_time + $response_time" | bc -l)
done

avg_time=$(echo "scale=3; $total_time / 5" | bc -l)
echo "Average Response Time: ${avg_time}s"
echo "Average Response Time: ${avg_time}s" >> test-results/test-report-${timestamp}.txt

# Test 5: System resource check
echo ""
echo -e "${BLUE}💾 Test 5: System Resource Check${NC}"
echo "[TEST 5] System Resource Check" >> test-results/test-report-${timestamp}.txt

# Check Node.js processes
if command -v ps &> /dev/null; then
    node_processes=$(ps aux | grep node | grep -v grep | wc -l)
    echo "Node.js process count: $node_processes"
    echo "Node.js process count: $node_processes" >> test-results/test-report-${timestamp}.txt
    
    # Check memory usage
    if [ $node_processes -gt 0 ]; then
        memory_usage=$(ps aux | grep node | grep -v grep | awk '{sum+=$6} END {print sum/1024 " MB"}')
        echo "Node.js memory usage: $memory_usage"
        echo "Node.js memory usage: $memory_usage" >> test-results/test-report-${timestamp}.txt
    fi
fi

# Generate test summary
echo ""
echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}📋 Test Completion Summary${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

echo "Test end time: $(date)" >> test-results/test-report-${timestamp}.txt
echo "========================================" >> test-results/test-report-${timestamp}.txt

# Display test results
cat test-results/test-report-${timestamp}.txt

echo ""
echo -e "${BLUE}📄 Detailed test report saved to: test-results/test-report-${timestamp}.txt${NC}"
echo ""

# Ask if demo test should be run
echo ""
read -p "Run demo scenario tests? (y/n): " demo_test
if [[ $demo_test == "y" || $demo_test == "Y" ]]; then
    echo ""
    echo -e "${BLUE}🎯 Starting demo scenario tests...${NC}"
    echo ""
    echo "Please follow these steps for manual testing:"
    echo ""
    echo "1. Open browser and visit: http://localhost:5173"
    echo "2. Test AI Assistant functionality"
    echo "3. Test Smart Forms"
    echo "4. Test Maintenance Dashboard"
    echo "5. Test Predictive Analytics"
    echo ""
    echo "For detailed test steps, refer to: TESTING_GUIDE.md"
    echo ""
    
    # Try to open browser
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:5173 &
    elif command -v open &> /dev/null; then
        open http://localhost:5173 &
    fi
    
    # Open test guide
    if [ -f "TD_Dev/TESTING_GUIDE.md" ]; then
        if command -v xdg-open &> /dev/null; then
            xdg-open "TD_Dev/TESTING_GUIDE.md" &
        elif command -v open &> /dev/null; then
            open "TD_Dev/TESTING_GUIDE.md" &
        else
            echo "Please manually open TD_Dev/TESTING_GUIDE.md for detailed test steps"
        fi
    fi
fi

echo ""
echo -e "${GREEN}🎉 Test script execution completed!${NC}"
echo ""
