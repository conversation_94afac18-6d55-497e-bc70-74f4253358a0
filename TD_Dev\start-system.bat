@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 Daikin AI System - One-Click Startup
echo ========================================
echo.

:: Set color
color 0A

:: Switch to project root directory
cd /d "%~dp0\.."

:: Check Node.js
echo 📋 Checking system environment...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Node.js not found
    echo Please download and install Node.js from https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js installed
node --version

:: Check npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: npm not found
    pause
    exit /b 1
)

echo ✅ npm installed
npm --version

:: Check Ollama service
echo.
echo 📡 Checking Ollama service...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Warning: Ollama service not running
    echo Attempting to start Ollama...
    start /B ollama serve
    timeout /t 5 /nobreak >nul

    :: Check again
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Failed to start Ollama service
        echo Please manually run: ollama serve
        echo Then restart this script
        pause
        exit /b 1
    )
)

echo ✅ Ollama service is running

:: Check llama3.1:8b model
echo.
echo 🤖 Checking AI model...
ollama list | findstr "llama3.1:8b" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  llama3.1:8b model not found
    echo Downloading model (this may take several minutes)...
    ollama pull llama3.1:8b
    if %errorlevel% neq 0 (
        echo ❌ Model download failed
        pause
        exit /b 1
    )
)

echo ✅ AI model ready

:: Install dependencies
echo.
echo 📦 Installing project dependencies...
if not exist "node_modules" (
    echo Installing dependency packages...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Dependency installation failed
        pause
        exit /b 1
    )
) else (
    echo ✅ Dependencies already installed
)

:: Start development server
echo.
echo 🌟 Starting Daikin AI System...
echo.
echo ========================================
echo 🎯 System startup successful!
echo ========================================
echo.
echo 📱 Access URL: http://localhost:5173
echo 🔧 Admin Panel: http://localhost:5173/dashboard
echo 🤖 AI Assistant: http://localhost:5173 (click AI Assistant tab)
echo.
echo 💡 Test Features:
echo   • Smart Forms
echo   • Maintenance Dashboard
echo   • Predictive Analytics
echo   • AI Assistant Chat
echo.
echo 🛑 Press Ctrl+C to stop server
echo ========================================
echo.

:: Open browser
timeout /t 3 /nobreak >nul
start http://localhost:5173

:: Start development server
npm run dev

pause
