@echo off
chcp 65001 >nul
title Daikin AI System - Main Console

:main_menu
:: Switch to project root directory
cd /d "%~dp0\.."

cls
echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██    🏭 DAIKIN AI SYSTEM - Management Console            ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.
echo 💡 Welcome to Daikin AI System Management Console
echo 📅 Current Time: %date% %time%
echo.
echo ========================================
echo 🚀 Main Menu - Please Select Option
echo ========================================
echo.
echo [1] 🔍 System Status Check
echo [2] 🚀 One-Click System Start
echo [3] 🎯 Quick Demo (5 minutes)
echo [4] 🧪 Run Complete Tests
echo [5] 📚 View Test Guide
echo [6] 🛠️ System Maintenance Tools
echo [7] 📊 View System Information
echo [8] ❌ Exit Program
echo.
echo ========================================

set /p choice="Please enter option (1-8): "

if "%choice%"=="1" goto check_system
if "%choice%"=="2" goto start_system
if "%choice%"=="3" goto quick_demo
if "%choice%"=="4" goto run_tests
if "%choice%"=="5" goto view_guide
if "%choice%"=="6" goto maintenance
if "%choice%"=="7" goto system_info
if "%choice%"=="8" goto exit_program

echo.
echo ❌ Invalid option, please select again
timeout /t 2 /nobreak >nul
goto main_menu

:check_system
cls
echo.
echo 🔍 Executing system status check...
echo.
call check-system.bat
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:start_system
cls
echo.
echo 🚀 Starting Daikin AI System...
echo.
call start-system.bat
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:quick_demo
cls
echo.
echo 🎯 Starting quick demo...
echo.
call quick-demo.bat
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:run_tests
cls
echo.
echo 🧪 Running complete test suite...
echo.
call run-tests.bat
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:view_guide
cls
echo.
echo 📚 Opening test guide...
echo.
if exist "TD_Dev\TESTING_GUIDE.md" (
    start notepad "TD_Dev\TESTING_GUIDE.md"
    echo ✅ Test guide opened in notepad
) else (
    echo ❌ Test guide file not found
)

if exist "COMPLETE_TEST_GUIDE.md" (
    echo.
    set /p open_complete="Also open complete test guide? (y/n): "
    if /i "!open_complete!"=="y" (
        start notepad COMPLETE_TEST_GUIDE.md
        echo ✅ Complete test guide opened
    )
)

echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:maintenance
cls
echo.
echo ========================================
echo 🛠️ System Maintenance Tools
echo ========================================
echo.
echo [1] 🧹 Clean cache and temporary files
echo [2] 🔄 Reinstall dependencies
echo [3] 📦 Update AI model
echo [4] 🔧 Reset system configuration
echo [5] 📋 View log files
echo [6] 🔙 Return to main menu
echo.

set /p maint_choice="Please select maintenance operation (1-6): "

if "%maint_choice%"=="1" goto clean_cache
if "%maint_choice%"=="2" goto reinstall_deps
if "%maint_choice%"=="3" goto update_model
if "%maint_choice%"=="4" goto reset_config
if "%maint_choice%"=="5" goto view_logs
if "%maint_choice%"=="6" goto main_menu

echo ❌ Invalid option
timeout /t 2 /nobreak >nul
goto maintenance

:clean_cache
echo.
echo 🧹 Cleaning cache and temporary files...
if exist "node_modules\.cache" (
    rmdir /s /q "node_modules\.cache"
    echo ✅ Cleaned node_modules cache
)
if exist "test-results" (
    echo Cleaning test result files...
    del /q "test-results\*.*" 2>nul
    echo ✅ Cleaned test results
)
npm cache clean --force 2>nul
echo ✅ Cleaned npm cache
echo.
echo Cache cleanup completed
timeout /t 3 /nobreak >nul
goto maintenance

:reinstall_deps
echo.
echo 🔄 Reinstalling project dependencies...
if exist "node_modules" (
    echo Removing existing dependencies...
    rmdir /s /q node_modules
)
if exist "package-lock.json" (
    del package-lock.json
)
echo Reinstalling dependencies...
npm install
echo ✅ Dependencies reinstalled successfully
timeout /t 3 /nobreak >nul
goto maintenance

:update_model
echo.
echo 📦 Updating AI model...
ollama pull llama3.1:8b
echo ✅ AI model updated successfully
timeout /t 3 /nobreak >nul
goto maintenance

:reset_config
echo.
echo 🔧 Resetting system configuration...
echo This will reset all configurations to default
set /p confirm="Confirm reset? (y/n): "
if /i "%confirm%"=="y" (
    echo Resetting configuration files...
    echo ✅ Configuration reset completed
) else (
    echo Reset operation cancelled
)
timeout /t 3 /nobreak >nul
goto maintenance

:view_logs
echo.
echo 📋 Viewing log files...
if exist "test-results" (
    echo Available test reports:
    dir /b test-results\*.txt 2>nul
    echo.
    set /p log_file="Enter log file name to view (or press Enter to skip): "
    if not "!log_file!"=="" (
        if exist "test-results\!log_file!" (
            type "test-results\!log_file!"
        ) else (
            echo File not found
        )
    )
) else (
    echo No log files found
)
echo.
echo Press any key to continue...
pause >nul
goto maintenance

:system_info
cls
echo.
echo ========================================
echo 📊 System Information
echo ========================================
echo.
echo 🖥️ Operating System Information:
systeminfo | findstr /C:"OS Name" /C:"OS Version" /C:"System Type"
echo.
echo 💾 Memory Information:
systeminfo | findstr /C:"Total Physical Memory" /C:"Available Physical Memory"
echo.
echo 🔧 Node.js Information:
node --version 2>nul && echo Node.js installed || echo Node.js not installed
npm --version 2>nul && echo npm installed || echo npm not installed
echo.
echo 🤖 Ollama Information:
ollama --version 2>nul && echo Ollama installed || echo Ollama not installed
echo.
echo 📁 Project Information:
if exist "package.json" (
    echo Project configuration:
    findstr "name\|version\|description" package.json 2>nul
) else (
    echo package.json not found
)
echo.
echo 🌐 Network Connection:
ping -n 1 google.com >nul 2>&1 && echo ✅ Network connection normal || echo ❌ Network connection abnormal
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:exit_program
cls
echo.
echo ========================================
echo 👋 Thank you for using Daikin AI System
echo ========================================
echo.
echo 🎯 System Features Summary:
echo   • AI Assistant - Knowledge Q&A and technical support
echo   • Smart Forms - Automated workflow processes
echo   • Maintenance Dashboard - Equipment monitoring and alerts
echo   • Predictive Analytics - Cost optimization and ROI calculation
echo.
echo 💰 Business Value:
echo   • Investment: RM240,000
echo   • Annual Savings: RM450,000+
echo   • ROI: 187.5%%
echo.
echo 📚 Related Documentation:
echo   • TESTING_GUIDE.md - Complete testing guide
echo   • COMPLETE_TEST_GUIDE.md - Demo scenario details
echo   • SETUP_GUIDE.md - System configuration guide
echo.
echo 🚀 To restart, run: main-menu.bat
echo.
echo Goodbye!
timeout /t 5 /nobreak >nul
exit
