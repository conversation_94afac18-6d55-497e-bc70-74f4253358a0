/**
 * Enhanced RAG Process Visualization
 * Shows the step-by-step process of RAG query processing with dynamic animations
 */

import React, { useState, useEffect, useRef } from 'react';
import {
    MagnifyingGlassIcon,
    DocumentTextIcon,
    CpuChipIcon,
    CheckCircleIcon,
    ClockIcon,
    BoltIcon,
    ExclamationTriangleIcon,
    EyeIcon,
    BookOpenIcon,
    LightBulbIcon
} from './icons/Icons';

interface RAGStep {
    id: string;
    title: string;
    description: string;
    status: 'pending' | 'processing' | 'completed' | 'error';
    duration?: number;
    details?: string[];
    subSteps?: string[];
    currentSubStep?: number;
    progress?: number;
    metrics?: {
        documentsFound?: number;
        relevanceScore?: number;
        confidenceLevel?: number;
    };
}

interface RAGProcessVisualizationProps {
    isProcessing: boolean;
    query?: string;
    onComplete?: () => void;
}

const RAGProcessVisualization: React.FC<RAGProcessVisualizationProps> = ({ 
    isProcessing, 
    query, 
    onComplete 
}) => {
    const [steps, setSteps] = useState<RAGStep[]>([
        {
            id: 'query_analysis',
            title: 'Query Understanding',
            description: 'Analyzing and understanding your question',
            status: 'pending',
            subSteps: [
                'Parsing natural language',
                'Extracting key concepts',
                'Identifying query intent',
                'Determining context requirements'
            ],
            currentSubStep: 0,
            progress: 0,
            metrics: {}
        },
        {
            id: 'permission_check',
            title: 'Permission Validation',
            description: 'Verifying access rights and security clearance',
            status: 'pending',
            subSteps: [
                'Checking user role permissions',
                'Validating document access rights',
                'Applying security filters',
                'Confirming data classification'
            ],
            currentSubStep: 0,
            progress: 0,
            metrics: {}
        },
        {
            id: 'document_search',
            title: 'Knowledge Retrieval',
            description: 'Searching through authorized knowledge base',
            status: 'pending',
            subSteps: [
                'Semantic vector search',
                'Relevance scoring',
                'Document ranking',
                'Source validation'
            ],
            currentSubStep: 0,
            progress: 0,
            metrics: { documentsFound: 0 }
        },
        {
            id: 'context_processing',
            title: 'Context Analysis',
            description: 'Processing and filtering relevant information',
            status: 'pending',
            subSteps: [
                'Content extraction',
                'Relevance filtering',
                'Context chunking',
                'Quality assessment'
            ],
            currentSubStep: 0,
            progress: 0,
            metrics: { relevanceScore: 0 }
        },
        {
            id: 'response_generation',
            title: 'Response Synthesis',
            description: 'Generating comprehensive answer with sources',
            status: 'pending',
            subSteps: [
                'Context integration',
                'Response formulation',
                'Source attribution',
                'Quality validation'
            ],
            currentSubStep: 0,
            progress: 0,
            metrics: { confidenceLevel: 0 }
        }
    ]);

    const [currentStepIndex, setCurrentStepIndex] = useState(-1);
    const [animationPhase, setAnimationPhase] = useState<'idle' | 'processing' | 'completed'>('idle');
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        if (!isProcessing) {
            setCurrentStepIndex(-1);
            setAnimationPhase('idle');
            setSteps(steps => steps.map(step => ({
                ...step,
                status: 'pending',
                currentSubStep: 0,
                progress: 0,
                metrics: step.id === 'document_search' ? { documentsFound: 0 } :
                        step.id === 'context_processing' ? { relevanceScore: 0 } :
                        step.id === 'response_generation' ? { confidenceLevel: 0 } : {}
            })));
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
            return;
        }

        setAnimationPhase('processing');
        let stepIndex = 0;

        const processStep = async () => {
            if (stepIndex >= steps.length) {
                setAnimationPhase('completed');
                onComplete?.();
                return;
            }

            setCurrentStepIndex(stepIndex);

            // Mark current step as processing
            setSteps(prevSteps =>
                prevSteps.map((step, index) => ({
                    ...step,
                    status: index === stepIndex ? 'processing' :
                           index < stepIndex ? 'completed' : 'pending'
                }))
            );

            // Simulate detailed sub-step processing
            const currentStep = steps[stepIndex];
            const subStepDuration = 300 + Math.random() * 400; // 300-700ms per sub-step
            const totalStepTime = subStepDuration * (currentStep.subSteps?.length || 1);

            // Animate sub-steps and progress
            let subStepIndex = 0;
            const subStepInterval = setInterval(() => {
                setSteps(prevSteps =>
                    prevSteps.map((step, index) => {
                        if (index === stepIndex) {
                            const progress = Math.min(100, ((subStepIndex + 1) / (step.subSteps?.length || 1)) * 100);
                            let updatedMetrics = { ...step.metrics };

                            // Simulate realistic metrics
                            if (step.id === 'document_search') {
                                updatedMetrics.documentsFound = Math.min(12, Math.floor(progress / 20) + Math.floor(Math.random() * 3));
                            } else if (step.id === 'context_processing') {
                                updatedMetrics.relevanceScore = Math.min(0.95, (progress / 100) * (0.8 + Math.random() * 0.15));
                            } else if (step.id === 'response_generation') {
                                updatedMetrics.confidenceLevel = Math.min(0.92, (progress / 100) * (0.75 + Math.random() * 0.17));
                            }

                            return {
                                ...step,
                                currentSubStep: subStepIndex,
                                progress,
                                metrics: updatedMetrics
                            };
                        }
                        return step;
                    })
                );

                subStepIndex++;
                if (subStepIndex >= (currentStep.subSteps?.length || 1)) {
                    clearInterval(subStepInterval);

                    // Mark step as completed
                    setTimeout(() => {
                        setSteps(prevSteps =>
                            prevSteps.map((step, index) => ({
                                ...step,
                                status: index <= stepIndex ? 'completed' : 'pending',
                                duration: index === stepIndex ? Math.round(totalStepTime) : step.duration
                            }))
                        );

                        stepIndex++;
                        setTimeout(processStep, 300); // Brief pause between steps
                    }, 200);
                }
            }, subStepDuration);
        };

        processStep();

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [isProcessing, onComplete]);

    const getStepIcon = (step: RAGStep) => {
        switch (step.id) {
            case 'query_analysis':
                return <LightBulbIcon className="w-5 h-5" />;
            case 'permission_check':
                return <EyeIcon className="w-5 h-5" />;
            case 'document_search':
                return <BookOpenIcon className="w-5 h-5" />;
            case 'context_processing':
                return <BoltIcon className="w-5 h-5" />;
            case 'response_generation':
                return <CpuChipIcon className="w-5 h-5" />;
            default:
                return <ClockIcon className="w-5 h-5" />;
        }
    };

    const getStepColor = (status: RAGStep['status']) => {
        switch (status) {
            case 'completed':
                return 'text-green-700 bg-green-50 border-green-200';
            case 'processing':
                return 'text-blue-700 bg-blue-50 border-blue-200';
            case 'error':
                return 'text-red-700 bg-red-50 border-red-200';
            default:
                return 'text-gray-500 bg-gray-50 border-gray-200';
        }
    };

    const getIconColor = (status: RAGStep['status']) => {
        switch (status) {
            case 'completed':
                return 'text-green-600 bg-green-100';
            case 'processing':
                return 'text-blue-600 bg-blue-100';
            case 'error':
                return 'text-red-600 bg-red-100';
            default:
                return 'text-gray-400 bg-gray-100';
        }
    };

    const getConnectorColor = (index: number) => {
        if (index >= steps.length - 1) return '';
        
        const currentStep = steps[index];
        const nextStep = steps[index + 1];
        
        if (currentStep.status === 'completed') {
            return 'bg-green-400';
        } else if (currentStep.status === 'processing') {
            return 'bg-blue-400';
        }
        return 'bg-gray-300';
    };

    if (!isProcessing && currentStepIndex === -1) {
        return null;
    }

    return (
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-4 shadow-sm">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${animationPhase === 'processing' ? 'bg-blue-100' : animationPhase === 'completed' ? 'bg-green-100' : 'bg-gray-100'}`}>
                        <CpuChipIcon className={`w-6 h-6 ${animationPhase === 'processing' ? 'text-blue-600' : animationPhase === 'completed' ? 'text-green-600' : 'text-gray-600'}`} />
                    </div>
                    <div>
                        <h3 className="font-bold text-gray-900">Intelligent RAG Processing</h3>
                        <p className="text-sm text-gray-600">Advanced retrieval-augmented generation pipeline</p>
                    </div>
                </div>

                {/* Overall Progress */}
                {isProcessing && (
                    <div className="flex items-center gap-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                                style={{ width: `${((currentStepIndex + 1) / steps.length) * 100}%` }}
                            />
                        </div>
                        <span className="text-sm text-gray-600 font-medium">
                            {Math.round(((currentStepIndex + 1) / steps.length) * 100)}%
                        </span>
                    </div>
                )}
            </div>

            {/* Query Display */}
            {query && (
                <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex items-start gap-3">
                        <MagnifyingGlassIcon className="w-5 h-5 text-gray-600 mt-0.5 flex-shrink-0" />
                        <div>
                            <p className="text-sm font-medium text-gray-700 mb-1">Processing Query:</p>
                            <p className="text-gray-900 text-sm leading-relaxed">"{query}"</p>
                        </div>
                    </div>
                </div>
            )}

            {/* Processing Steps */}
            <div className="space-y-6">
                {steps.map((step, index) => (
                    <div key={step.id} className="relative">
                        {/* Step Content */}
                        <div className={`rounded-xl border-2 transition-all duration-500 ${getStepColor(step.status)} ${
                            step.status === 'processing' ? 'shadow-lg scale-[1.02]' : ''
                        }`}>
                            <div className="p-5">
                                {/* Step Header */}
                                <div className="flex items-start gap-4 mb-4">
                                    {/* Step Icon */}
                                    <div className={`flex-shrink-0 p-3 rounded-xl transition-all duration-300 ${getIconColor(step.status)}`}>
                                        {step.status === 'completed' ? (
                                            <CheckCircleIcon className="w-6 h-6" />
                                        ) : step.status === 'processing' ? (
                                            <div className="w-6 h-6 border-3 border-current border-t-transparent rounded-full animate-spin" />
                                        ) : (
                                            getStepIcon(step)
                                        )}
                                    </div>

                                    {/* Step Info */}
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between mb-2">
                                            <h4 className="font-bold text-lg">{step.title}</h4>
                                            <div className="flex items-center gap-3">
                                                {step.status === 'processing' && (
                                                    <div className="flex items-center gap-2">
                                                        <div className="w-20 bg-gray-200 rounded-full h-1.5">
                                                            <div
                                                                className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                                                                style={{ width: `${step.progress || 0}%` }}
                                                            />
                                                        </div>
                                                        <span className="text-xs font-medium text-blue-600">
                                                            {Math.round(step.progress || 0)}%
                                                        </span>
                                                    </div>
                                                )}
                                                {step.duration && (
                                                    <span className="text-xs font-medium px-2 py-1 bg-gray-100 rounded-full">
                                                        {step.duration}ms
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <p className="text-sm text-gray-600 mb-3">{step.description}</p>

                                        {/* Metrics Display */}
                                        {(step.status === 'processing' || step.status === 'completed') && step.metrics && (
                                            <div className="flex items-center gap-4 mb-3">
                                                {step.metrics.documentsFound !== undefined && (
                                                    <div className="flex items-center gap-2 text-xs">
                                                        <DocumentTextIcon className="w-4 h-4 text-blue-600" />
                                                        <span className="font-medium">{step.metrics.documentsFound} docs found</span>
                                                    </div>
                                                )}
                                                {step.metrics.relevanceScore !== undefined && step.metrics.relevanceScore > 0 && (
                                                    <div className="flex items-center gap-2 text-xs">
                                                        <BoltIcon className="w-4 h-4 text-orange-600" />
                                                        <span className="font-medium">Relevance: {(step.metrics.relevanceScore * 100).toFixed(1)}%</span>
                                                    </div>
                                                )}
                                                {step.metrics.confidenceLevel !== undefined && step.metrics.confidenceLevel > 0 && (
                                                    <div className="flex items-center gap-2 text-xs">
                                                        <CheckCircleIcon className="w-4 h-4 text-green-600" />
                                                        <span className="font-medium">Confidence: {(step.metrics.confidenceLevel * 100).toFixed(1)}%</span>
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                        {/* Sub-steps */}
                                        {(step.status === 'processing' || step.status === 'completed') && step.subSteps && (
                                            <div className="space-y-2">
                                                {step.subSteps.map((subStep, subIndex) => {
                                                    const isCompleted = step.status === 'completed' ||
                                                                      (step.status === 'processing' && subIndex <= (step.currentSubStep || 0));
                                                    const isCurrent = step.status === 'processing' && subIndex === (step.currentSubStep || 0);

                                                    return (
                                                        <div key={subIndex} className={`flex items-center gap-3 text-sm transition-all duration-300 ${
                                                            isCompleted ? 'text-gray-700' : 'text-gray-400'
                                                        }`}>
                                                            <div className={`w-2 h-2 rounded-full transition-all duration-300 ${
                                                                isCompleted ? 'bg-green-500' :
                                                                isCurrent ? 'bg-blue-500 animate-pulse' : 'bg-gray-300'
                                                            }`} />
                                                            <span className={`${isCurrent ? 'font-medium' : ''}`}>
                                                                {subStep}
                                                            </span>
                                                            {isCurrent && (
                                                                <div className="flex items-center gap-1 ml-auto">
                                                                    <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" />
                                                                    <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                                                                    <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                                                                </div>
                                                            )}
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Connector Line */}
                        {index < steps.length - 1 && (
                            <div className="flex justify-center py-3">
                                <div className={`w-1 h-8 rounded-full transition-all duration-500 ${getConnectorColor(index)}`} />
                            </div>
                        )}
                    </div>
                ))}
            </div>

            {/* Status Summary */}
            <div className="mt-6 pt-6 border-t border-gray-200">
                {isProcessing && (
                    <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                                <div>
                                    <p className="text-sm font-bold text-blue-900">
                                        Processing with Advanced RAG Technology
                                    </p>
                                    <p className="text-xs text-blue-700">
                                        {currentStepIndex >= 0 && currentStepIndex < steps.length
                                            ? `Currently: ${steps[currentStepIndex].title}`
                                            : 'Initializing...'}
                                    </p>
                                </div>
                            </div>
                            <div className="text-right">
                                <p className="text-xs text-blue-600 font-medium">
                                    Step {currentStepIndex + 1} of {steps.length}
                                </p>
                                <p className="text-xs text-blue-500">
                                    {Math.round(((currentStepIndex + 1) / steps.length) * 100)}% Complete
                                </p>
                            </div>
                        </div>
                    </div>
                )}

                {!isProcessing && steps.every(step => step.status === 'completed') && (
                    <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <CheckCircleIcon className="w-5 h-5 text-green-600" />
                                <div>
                                    <p className="text-sm font-bold text-green-900">
                                        RAG Processing Completed Successfully
                                    </p>
                                    <p className="text-xs text-green-700">
                                        Your query has been processed with high accuracy
                                    </p>
                                </div>
                            </div>
                            <div className="text-right">
                                <p className="text-xs text-green-600 font-medium">
                                    Total Time: {steps.reduce((sum, step) => sum + (step.duration || 0), 0)}ms
                                </p>
                                <p className="text-xs text-green-500">
                                    {steps.length} steps completed
                                </p>
                            </div>
                        </div>

                        {/* Final Metrics Summary */}
                        <div className="mt-3 pt-3 border-t border-green-200">
                            <div className="grid grid-cols-3 gap-4 text-center">
                                <div>
                                    <p className="text-lg font-bold text-green-800">
                                        {steps.find(s => s.id === 'document_search')?.metrics?.documentsFound || 0}
                                    </p>
                                    <p className="text-xs text-green-600">Documents Retrieved</p>
                                </div>
                                <div>
                                    <p className="text-lg font-bold text-green-800">
                                        {((steps.find(s => s.id === 'context_processing')?.metrics?.relevanceScore || 0) * 100).toFixed(0)}%
                                    </p>
                                    <p className="text-xs text-green-600">Relevance Score</p>
                                </div>
                                <div>
                                    <p className="text-lg font-bold text-green-800">
                                        {((steps.find(s => s.id === 'response_generation')?.metrics?.confidenceLevel || 0) * 100).toFixed(0)}%
                                    </p>
                                    <p className="text-xs text-green-600">Confidence Level</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default RAGProcessVisualization;
