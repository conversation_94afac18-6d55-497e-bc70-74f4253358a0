
import React, { useState, useContext } from 'react';
import type { FormTemplate, FormField, Ticket, Activity, View, AnalyzedTicketInfo } from '../types';
import { AppContext } from './AppContext';
import { generateFormSchema, analyzeTicketDescription, suggestAssignees, generateTicketWorkPlan } from '../services/ollamaService';
import { BotIcon, PlusIcon, PencilIcon, TicketIcon, CheckIcon, XMarkIcon, ClipboardDocumentListIcon } from './icons/Icons';
import TicketingSystem from './TicketingSystem';
import { AISummaryActivity, AssigneeList } from './TicketDetailView'; // Re-use components

type IconElement = React.ReactElement<React.SVGProps<SVGSVGElement>>;

// --- Template Management Tab ---
const TemplateManagement: React.FC = () => {
    const { formTemplates, updateFormTemplate, deleteFormTemplate } = useContext(AppContext);
    const [editingTemplate, setEditingTemplate] = useState<FormTemplate | null>(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

    const handleEdit = (template: FormTemplate) => {
        setEditingTemplate({ ...template });
    };

    const handleSaveEdit = () => {
        if (editingTemplate) {
            updateFormTemplate(editingTemplate);
            setEditingTemplate(null);
        }
    };

    const handleDelete = (templateId: string) => {
        deleteFormTemplate(templateId);
        setShowDeleteConfirm(null);
    };

    const handleFieldChange = (fieldIndex: number, field: FormField) => {
        if (editingTemplate) {
            const updatedFields = [...editingTemplate.fields];
            updatedFields[fieldIndex] = field;
            setEditingTemplate({ ...editingTemplate, fields: updatedFields });
        }
    };

    const addField = () => {
        if (editingTemplate) {
            const newField: FormField = {
                name: 'new_field',
                label: 'New Field',
                type: 'text',
                required: false
            };
            setEditingTemplate({
                ...editingTemplate,
                fields: [...editingTemplate.fields, newField]
            });
        }
    };

    const removeField = (fieldIndex: number) => {
        if (editingTemplate) {
            const updatedFields = editingTemplate.fields.filter((_, index) => index !== fieldIndex);
            setEditingTemplate({ ...editingTemplate, fields: updatedFields });
        }
    };

    if (editingTemplate) {
        return (
            <div className="p-8 max-w-4xl mx-auto h-full overflow-y-auto">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">Edit Template</h2>
                    <div className="flex gap-2">
                        <button
                            onClick={() => setEditingTemplate(null)}
                            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={handleSaveEdit}
                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                        >
                            Save Changes
                        </button>
                    </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6 space-y-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Template Name</label>
                        <input
                            type="text"
                            value={editingTemplate.name}
                            onChange={(e) => setEditingTemplate({ ...editingTemplate, name: e.target.value })}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea
                            value={editingTemplate.description}
                            onChange={(e) => setEditingTemplate({ ...editingTemplate, description: e.target.value })}
                            rows={3}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>

                    <div>
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">Form Fields</h3>
                            <button
                                onClick={addField}
                                className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center gap-1"
                            >
                                <PlusIcon className="w-4 h-4" />
                                Add Field
                            </button>
                        </div>

                        <div className="space-y-4">
                            {editingTemplate.fields.map((field, index) => (
                                <div key={index} className="border border-gray-200 rounded-lg p-4">
                                    <div className="flex items-center justify-between mb-3">
                                        <h4 className="font-medium text-gray-900">Field {index + 1}</h4>
                                        <button
                                            onClick={() => removeField(index)}
                                            className="text-red-600 hover:text-red-800"
                                        >
                                            <XMarkIcon className="w-4 h-4" />
                                        </button>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Field Name</label>
                                            <input
                                                type="text"
                                                value={field.name}
                                                onChange={(e) => handleFieldChange(index, { ...field, name: e.target.value })}
                                                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Label</label>
                                            <input
                                                type="text"
                                                value={field.label}
                                                onChange={(e) => handleFieldChange(index, { ...field, label: e.target.value })}
                                                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                                            <select
                                                value={field.type}
                                                onChange={(e) => handleFieldChange(index, { ...field, type: e.target.value as FormField['type'] })}
                                                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                                            >
                                                <option value="text">Text</option>
                                                <option value="email">Email</option>
                                                <option value="number">Number</option>
                                                <option value="textarea">Textarea</option>
                                                <option value="select">Select</option>
                                                <option value="file">File</option>
                                            </select>
                                        </div>
                                        <div className="flex items-center">
                                            <label className="flex items-center">
                                                <input
                                                    type="checkbox"
                                                    checked={field.required}
                                                    onChange={(e) => handleFieldChange(index, { ...field, required: e.target.checked })}
                                                    className="mr-2"
                                                />
                                                Required
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="p-8 h-full overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">Manage Templates</h2>
                    <p className="text-gray-500">Edit or delete existing form templates</p>
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {formTemplates.map(template => (
                    <div key={template.id} className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="flex items-start justify-between mb-4">
                            <div>
                                <h3 className="font-bold text-lg text-gray-900">{template.name}</h3>
                                <p className="text-sm text-gray-500 mt-1">{template.description}</p>
                                <p className="text-xs text-gray-400 mt-2">{template.fields.length} fields</p>
                            </div>
                        </div>

                        <div className="flex gap-2">
                            <button
                                onClick={() => handleEdit(template)}
                                className="flex-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center justify-center gap-1"
                            >
                                <PencilIcon className="w-4 h-4" />
                                Edit
                            </button>
                            <button
                                onClick={() => setShowDeleteConfirm(template.id)}
                                className="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
                            >
                                <XMarkIcon className="w-4 h-4" />
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {/* Delete Confirmation Modal */}
            {showDeleteConfirm && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                        <h3 className="text-lg font-bold text-gray-900 mb-2">Delete Template</h3>
                        <p className="text-gray-600 mb-4">Are you sure you want to delete this template? This action cannot be undone.</p>
                        <div className="flex gap-2 justify-end">
                            <button
                                onClick={() => setShowDeleteConfirm(null)}
                                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={() => handleDelete(showDeleteConfirm)}
                                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

// --- Form Creation Tab ---
const FormCreation: React.FC<{onTemplateCreated: () => void}> = ({ onTemplateCreated }) => {
    // ... (logic from existing file, but add onTemplateCreated callback)
    const { addFormTemplate } = useContext(AppContext);
    const [prompt, setPrompt] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [generatedFields, setGeneratedFields] = useState<FormField[] | null>(null);
    const [templateName, setTemplateName] = useState('');

    const handleGenerate = async () => {
        if (!prompt.trim()) return;
        setIsLoading(true);
        setGeneratedFields(null);
        try {
            const fields = await generateFormSchema(prompt);
            setGeneratedFields(fields);
            setTemplateName(prompt);
        } catch (e) { console.error(e); } finally { setIsLoading(false); }
    };
    
    const handleSave = () => {
        if (!generatedFields || !templateName.trim()) return;
        const newTemplate: FormTemplate = {
            id: `tpl-${Date.now()}`,
            name: templateName,
            description: prompt,
            fields: generatedFields
        };
        addFormTemplate(newTemplate);
        setGeneratedFields(null);
        setPrompt('');
        setTemplateName('');
        onTemplateCreated(); // Switch back to fill tab
    }
    // ... (rest of the component JSX from existing file)
    return (
        <div className="p-8 max-w-4xl mx-auto h-full overflow-y-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">AI Form Builder</h2>
            <p className="text-gray-500 mb-6">Describe the form you want to create, and our AI will generate the structure for you.</p>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
                 <label htmlFor="description" className="text-sm font-medium text-gray-600 mb-2 block">
                    Form Description
                </label>
                <textarea
                    id="description"
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    rows={4}
                    className="w-full bg-gray-50 border-gray-300 rounded-md p-3 focus:ring-blue-500 focus:border-blue-500 text-sm text-gray-800"
                    placeholder="e.g., A form to request new IT hardware including item type, quantity, and justification."
                />
                <button onClick={handleGenerate} disabled={isLoading || !prompt.trim()} className="mt-4 px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 text-sm font-semibold flex items-center gap-2 disabled:bg-blue-400 disabled:cursor-wait">
                    <BotIcon className="w-4 h-4"/>
                    {isLoading ? 'Generating...' : 'Generate Form'}
                </button>
            </div>
            
            {generatedFields && (
                <div className="mt-8 bg-white border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Generated Form Preview</h3>
                     <div className="mb-4">
                        <label className="text-sm font-medium text-gray-600 mb-1 block">Template Name</label>
                        <input type="text" value={templateName} onChange={e => setTemplateName(e.target.value)} className="w-full bg-gray-50 border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-gray-800" />
                    </div>
                    <div className="space-y-4 border border-dashed border-gray-300 p-4 rounded-md">
                        {generatedFields.map(field => (
                            <div key={field.name}>
                                <label className="text-sm font-medium text-gray-700">{field.label} {field.required && <span className="text-red-500">*</span>}</label>
                                <p className="text-xs text-gray-400">name: {field.name}, type: {field.type}</p>
                                <input type={field.type} disabled className="mt-1 w-full bg-gray-200 border-gray-300 rounded-md p-2 text-sm text-gray-500 cursor-not-allowed"/>
                            </div>
                        ))}
                    </div>
                    <button onClick={handleSave} disabled={!templateName.trim()} className="mt-4 px-4 py-2 rounded-lg bg-green-600 text-white hover:bg-green-700 text-sm font-semibold flex items-center gap-2">
                        Save Template
                    </button>
                </div>
            )}
        </div>
    );
};

// --- Form Filling Logic ---

const ConversationalForm: React.FC<{ template: FormTemplate; onComplete: (data: Record<string, any>) => void; onCancel: () => void; }> = ({ template, onComplete, onCancel }) => {
    // This is the same as the existing component.
    const [step, setStep] = useState(0);
    const [formData, setFormData] = useState<Record<string, any>>({});
    const [currentValue, setCurrentValue] = useState<any>('');
    const currentField = template.fields[step];
    const handleNext = () => {
        const newFormData = {...formData, [currentField.name]: currentValue };
        setFormData(newFormData);
        setCurrentValue('');
        if (step < template.fields.length - 1) {
            setStep(step + 1);
        } else {
            onComplete(newFormData);
        }
    };
    return (
         <div className="p-8 max-w-2xl mx-auto h-full overflow-y-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">{template.name}</h2>
            <p className="text-gray-500 mb-6">The AI will guide you through filling out this form.</p>
            <div className="bg-white border border-gray-200 rounded-lg p-6 space-y-4">
                <div className="flex items-start gap-3">
                    <div className="w-8 h-8 rounded-full bg-blue-600 flex-shrink-0 flex items-center justify-center"><BotIcon className="w-5 h-5 text-white"/></div>
                    <div className="bg-gray-100 p-3 rounded-lg"><p className="text-gray-800">{currentField.label}{currentField.required ? '' : ' (optional)'}</p></div>
                </div>
                <div>
                     {currentField.type === 'textarea' ? ( <textarea value={currentValue} onChange={e => setCurrentValue(e.target.value)} rows={4} className="w-full bg-gray-50 border-gray-300 rounded-md p-3 focus:ring-blue-500 focus:border-blue-500 text-sm text-gray-800" />
                    ) : currentField.type === 'select' ? ( <select value={currentValue} onChange={e => setCurrentValue(e.target.value)} className="w-full bg-gray-50 border-gray-300 rounded-md p-3 focus:ring-blue-500 focus:border-blue-500 text-sm text-gray-800"><option value="">Select an option</option>{currentField.options?.map(opt => <option key={opt} value={opt}>{opt}</option>)}</select>
                    ) : currentField.type === 'file' ? ( <input type="file" onChange={e => setCurrentValue(e.target.files ? e.target.files[0] : null)} className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200" />
                    ) : ( <input type={currentField.type} value={currentValue} onChange={e => setCurrentValue(e.target.value)} className="w-full bg-gray-50 border-gray-300 rounded-md p-3 focus:ring-blue-500 focus:border-blue-500 text-sm text-gray-800" /> )}
                </div>
                <div className="pt-4 flex justify-end gap-3">
                    <button onClick={onCancel} className="px-4 py-2 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 text-sm font-semibold">Cancel</button>
                    <button onClick={handleNext} disabled={currentField.required && !currentValue} className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 text-sm font-semibold disabled:bg-blue-300 disabled:cursor-not-allowed">
                        {step === template.fields.length - 1 ? 'Finish & Review' : 'Next'}
                    </button>
                </div>
            </div>
         </div>
    );
};


// NEW Review and Confirm Component
const ReviewAndConfirm: React.FC<{
    aiAnalysis: AnalyzedTicketInfo;
    onConfirm: () => void;
    onCancel: () => void;
}> = ({ aiAnalysis, onConfirm, onCancel }) => {
    return (
        <div className="p-8 max-w-3xl mx-auto h-full overflow-y-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Review & Confirm Ticket</h2>
            <p className="text-gray-500 mb-6">Please review the details generated by the AI before creating the ticket.</p>
            <div className="bg-white border border-gray-200 rounded-lg p-6 space-y-6">
                <div>
                    <h3 className="font-semibold text-lg text-gray-900">{aiAnalysis.title}</h3>
                    <div className="flex items-center gap-2 mt-1">
                        <span className="text-sm font-semibold px-2 py-0.5 bg-gray-100 text-gray-800 rounded">{aiAnalysis.category}</span>
                        <span className={`text-sm font-semibold px-2 py-0.5 rounded ${aiAnalysis.urgency === 'High' ? 'bg-red-100 text-red-800' : aiAnalysis.urgency === 'Medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                            {aiAnalysis.urgency}
                        </span>
                    </div>
                </div>
                
                <AssigneeList userIds={aiAnalysis.suggestedAssignees} title="Suggested Assignees" />

                <AISummaryActivity summary={aiAnalysis.workPlan} timestamp="Just now" />
                
                <div className="pt-4 flex justify-end gap-3">
                    <button onClick={onCancel} className="px-4 py-2 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 text-sm font-semibold">Back</button>
                    <button onClick={onConfirm} className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 text-sm font-semibold flex items-center gap-2">
                        <CheckIcon className="w-4 h-4" />
                        Confirm and Create Ticket
                    </button>
                </div>
            </div>
        </div>
    );
};


const FormFillingController: React.FC<{ onTicketCreated: (view: View) => void; }> = ({ onTicketCreated }) => {
    const { formTemplates, addTicket, users, currentUser } = useContext(AppContext);
    const [selectedTemplate, setSelectedTemplate] = useState<FormTemplate | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [aiAnalysis, setAiAnalysis] = useState<AnalyzedTicketInfo | null>(null);
    const [rawFormData, setRawFormData] = useState<Record<string, any> | null>(null);

    const handleFormComplete = async (data: Record<string, any>) => {
        if (!selectedTemplate) return;
        setIsLoading(true);
        setRawFormData(data); // Save raw form data
        
        try {
            const description = Object.entries(data).filter(([key, value]) => value && typeof value !== 'object').map(([key, value]) => `${key}: ${value}`).join('\n');
            const analysis = await analyzeTicketDescription(description);
            const assignees = await suggestAssignees({title: analysis.title, description, category: analysis.category}, users);
            const workPlan = await generateTicketWorkPlan(analysis.title, description);
            
            setAiAnalysis({ ...analysis, suggestedAssignees: assignees, workPlan });

        } catch (error) {
            console.error("Failed during AI analysis", error);
        } finally {
            setIsLoading(false);
        }
    };
    
    const handleConfirmTicketCreation = () => {
        if (!aiAnalysis || !rawFormData || !selectedTemplate) return;

        let attachments: Ticket['attachments'] = [];
        const fileField = selectedTemplate.fields.find(f => f.type === 'file');
        if (fileField && rawFormData[fileField.name]) {
            const file = rawFormData[fileField.name] as File;
            attachments.push({ name: file.name, size: `${(file.size / 1024).toFixed(1)} KB` });
        }
        
        const description = Object.entries(rawFormData).filter(([key, value]) => value && typeof value !== 'object').map(([key, value]) => `${key}: ${value}`).join('\n');

        const finalTicket: Ticket = {
            id: `TICK-${Date.now()}`,
            title: aiAnalysis.title,
            description,
            status: 'Received',
            category: aiAnalysis.category,
            urgency: aiAnalysis.urgency,
            assignees: aiAnalysis.suggestedAssignees,
            createdAt: new Date().toISOString(),
            activity: [{
                id: `act-${Date.now() + 1}`,
                type: 'AI_SUMMARY',
                timestamp: new Date().toLocaleString(),
                content: JSON.stringify(aiAnalysis.workPlan),
            }, {
                id: `act-${Date.now()}`,
                type: 'CREATED',
                content: `Ticket created by ${currentUser.name} via ${selectedTemplate.name} form.`,
                timestamp: new Date().toLocaleString(),
                user: {id: currentUser.id, name: currentUser.name, avatar: currentUser.avatar}
            }],
            attachments,
        };
        
        addTicket(finalTicket);
        onTicketCreated(`detail-${finalTicket.id}`);
    };

    const resetState = () => {
        setSelectedTemplate(null);
        setIsLoading(false);
        setAiAnalysis(null);
        setRawFormData(null);
    }

    if (isLoading) {
        return <div className="p-10 flex flex-col items-center justify-center text-center h-full"><div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mb-4"></div><h3 className="text-xl font-bold text-gray-900">AI is analyzing your request...</h3></div>;
    }
    
    if (aiAnalysis) {
        return <ReviewAndConfirm aiAnalysis={aiAnalysis} onConfirm={handleConfirmTicketCreation} onCancel={() => setAiAnalysis(null)} />
    }
    
    if (selectedTemplate) {
        return <ConversationalForm template={selectedTemplate} onComplete={handleFormComplete} onCancel={resetState} />;
    }

    // Template selection screen
    return (
        <div className="p-8 h-full overflow-y-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Fill a Form</h2>
            <p className="text-gray-500 mb-6">Select a form template to start a request. An AI assistant will guide you through the process.</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {formTemplates.map(template => ( <button key={template.id} onClick={() => setSelectedTemplate(template)} className="bg-white border border-gray-200 rounded-lg p-6 text-left hover:border-blue-500 hover:bg-gray-50 transition-colors"><h3 className="font-bold text-lg text-gray-900">{template.name}</h3><p className="text-sm text-gray-500 mt-1">{template.description}</p></button>))}
                <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center text-center p-6 text-gray-500"><PlusIcon className="w-8 h-8 mb-2"/><h3 className="font-bold text-gray-600">Create New Template</h3><p className="text-sm">Go to the "Create Form Template" tab to build a new form with AI.</p></div>
            </div>
        </div>
    );
};

// --- Main Page Component ---
const SmartFormPage: React.FC = () => {
    type Tab = 'fill' | 'create' | 'manage' | 'tickets';
    const [activeTab, setActiveTab] = useState<Tab>('fill');
    
    // Lift state up from TicketingSystem
    const [activeTicketingView, setActiveTicketingView] = useState<View>('dashboard');

    const handleTicketCreated = (view: View) => {
        setActiveTicketingView(view);
        setActiveTab('tickets');
    }

    const renderContent = () => {
        switch (activeTab) {
            case 'fill': return <FormFillingController onTicketCreated={handleTicketCreated} />;
            case 'create': return <FormCreation onTemplateCreated={() => setActiveTab('fill')}/>;
            case 'manage': return <TemplateManagement />;
            case 'tickets': return <TicketingSystem activeView={activeTicketingView} onActiveViewChange={setActiveTicketingView} />;
            default: return null;
        }
    };
    
    return (
        <div className="flex-1 flex flex-col overflow-hidden">
            <header className="px-6 py-2 bg-white border-b border-gray-200 flex items-center gap-6">
                <TabButton label="Fill a Form" icon={<PencilIcon />} active={activeTab === 'fill'} onClick={() => setActiveTab('fill')} />
                <TabButton label="Create Template" icon={<PlusIcon />} active={activeTab === 'create'} onClick={() => setActiveTab('create')} />
                <TabButton label="Manage Templates" icon={<ClipboardDocumentListIcon />} active={activeTab === 'manage'} onClick={() => setActiveTab('manage')} />
                <TabButton label="Ticketing System" icon={<TicketIcon />} active={activeTab === 'tickets'} onClick={() => { setActiveTab('tickets'); setActiveTicketingView('dashboard'); }} />
            </header>
            <div className="flex-1 flex flex-col bg-gray-50 overflow-hidden">
                {renderContent()}
            </div>
        </div>
    );
};

interface TabButtonProps { label: string, icon: IconElement, active: boolean, onClick: () => void }
const TabButton: React.FC<TabButtonProps> = ({label, icon, active, onClick}) => (
     <button onClick={onClick} className={`flex items-center gap-2 py-3 px-1 text-sm font-medium transition-colors border-b-2 ${active ? 'text-blue-600 border-blue-600' : 'text-gray-500 border-transparent hover:text-blue-600 hover:border-blue-500'}`}>
        {React.cloneElement(icon, { className: 'w-5 h-5' })}
        {label}
    </button>
);


export default SmartFormPage;