
import React, { useState, useRef, useEffect } from 'react';
import type { User } from '../types';

interface UserSelectorProps {
  users: User[];
  loggedInUser: User; // The actual user who is logged in
  selectedUser: User; // The user being impersonated
  onSelectUser: (user: User) => void;
}

const UserSelector: React.FC<UserSelectorProps> = ({ users, loggedInUser, selectedUser, onSelectUser }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // The dropdown is only available if the *logged in* user is a Super User
  const isSuperUser = loggedInUser.role === 'Super User';
  const statusColor = selectedUser.status === 'Online' ? 'bg-green-500' : selectedUser.status === 'On Leave' ? 'bg-gray-400' : 'bg-yellow-400';

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelect = (user: User) => {
    onSelectUser(user);
    setIsOpen(false);
  };
  
  const toggleDropdown = () => {
    if (isSuperUser) {
      setIsOpen(!isOpen);
    }
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button 
        className={`flex items-center gap-3 p-2 rounded-lg ${isSuperUser ? 'hover:bg-gray-100 cursor-pointer' : 'cursor-default'}`}
        onClick={toggleDropdown}
        aria-haspopup="true"
        aria-expanded={isOpen}
      >
        <div className="w-9 h-9 rounded-full bg-gray-200 flex items-center justify-center relative">
          <span className="font-bold text-gray-700">{selectedUser.avatar}</span>
          <span className={`absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full ${statusColor} ring-2 ring-white`}></span>
        </div>
        <div>
          <p className="font-semibold text-sm text-left text-gray-800">{selectedUser.name}</p>
          <p className="text-xs text-left text-gray-500">{selectedUser.role} {selectedUser.id === loggedInUser.id && '(Me)'}</p>
        </div>
      </button>

      {isSuperUser && isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-80 overflow-y-auto">
          <ul>
            {/* Allow switching back to the Super User */}
            {users.find(u => u.role === 'Super User') && (
                <li>
                    <button onClick={() => handleSelect(users.find(u => u.role === 'Super User')!)} className="w-full text-left flex items-center gap-3 p-3 hover:bg-blue-50">
                        <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center font-bold text-sm text-white">
                            {users.find(u => u.role === 'Super User')!.avatar}
                        </div>
                        <div>
                            <p className="font-semibold text-sm text-gray-800">{users.find(u => u.role === 'Super User')!.name}</p>
                            <p className="text-xs text-gray-500">Super User (Me)</p>
                        </div>
                    </button>
                </li>
            )}
            {users.filter(u => u.role !== 'Super User').map(user => (
              <li key={user.id}>
                <button 
                  onClick={() => handleSelect(user)} 
                  className="w-full text-left flex items-center gap-3 p-3 hover:bg-blue-50"
                >
                  <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center font-bold text-sm text-gray-700">
                    {user.avatar}
                  </div>
                  <div>
                     <p className="font-semibold text-sm text-gray-800">{user.name}</p>
                     <p className="text-xs text-gray-500">{user.role}</p>
                  </div>
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default UserSelector;