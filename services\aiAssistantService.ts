import axios from 'axios';
import type { AIAssistantKnowledge, User, EnhancedRAGResponse, RAGSource, RAGMetrics, ExpertRecommendation, KnowledgeArticle, Announcement, Policy } from '../types';
import { AI_ASSISTANT_KNOWLEDGE_BASE, WIKI_KNOWLEDGE_ARTICLES, WIKI_ANNOUNCEMENTS, WIKI_POLICIES } from '../constants';

// Ollama Configuration
const OLLAMA_CONFIG = {
    base_url: 'http://localhost:11434',
    model: 'llama3.1:8b',
    timeout: 30000,
};

// Helper function to call Ollama API for chat
const callOllamaChat = async (prompt: string, systemPrompt?: string): Promise<string> => {
    try {
        const response = await axios.post(`${OLLAMA_CONFIG.base_url}/api/generate`, {
            model: OLLAMA_CONFIG.model,
            prompt: systemPrompt ? `${systemPrompt}\n\nUser: ${prompt}` : prompt,
            stream: false,
            options: {
                temperature: 0.7,
                top_p: 0.9,
                max_tokens: 500,
            }
        }, {
            timeout: OLLAMA_CONFIG.timeout,
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        return response.data.response || '';
    } catch (error) {
        console.error('Error calling Ollama API for chat:', error);
        throw error;
    }
};

// Convert Wiki content to AI Assistant knowledge base format
const convertWikiToKnowledge = (): AIAssistantKnowledge[] => {
    const wikiKnowledge: AIAssistantKnowledge[] = [];

    // Convert Knowledge Articles
    WIKI_KNOWLEDGE_ARTICLES.forEach(article => {
        wikiKnowledge.push({
            id: `WIKI-${article.id}`,
            question: `What is ${article.title}?`,
            answer: article.content,
            source: `Wiki Knowledge Article: ${article.title}`,
            quote: article.content.substring(0, 200) + (article.content.length > 200 ? '...' : ''),
            tags: [...article.tags, 'wiki', 'knowledge', article.category.toLowerCase().replace(/\s+/g, '_')],
            accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
            documentId: article.id,
            documentType: 'knowledge_article',
            expertAuthor: article.author + (article.authorDepartment ? ` - ${article.authorDepartment}` : ''),
            lastUpdated: article.publishDate,
            successRate: 90 + Math.floor(Math.random() * 8), // 90-97%
            relatedDocuments: []
        });

        // Add alternative questions for better matching
        const alternativeQuestions = generateAlternativeQuestions(article.title, article.content);
        alternativeQuestions.forEach((altQuestion, index) => {
            wikiKnowledge.push({
                id: `WIKI-${article.id}-ALT${index + 1}`,
                question: altQuestion,
                answer: article.content,
                source: `Wiki Knowledge Article: ${article.title}`,
                quote: article.content.substring(0, 200) + (article.content.length > 200 ? '...' : ''),
                tags: [...article.tags, 'wiki', 'knowledge', article.category.toLowerCase().replace(/\s+/g, '_')],
                accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
                documentId: article.id,
                documentType: 'knowledge_article',
                expertAuthor: article.author + (article.authorDepartment ? ` - ${article.authorDepartment}` : ''),
                lastUpdated: article.publishDate,
                successRate: 88 + Math.floor(Math.random() * 7), // 88-94%
                relatedDocuments: []
            });
        });
    });

    // Convert Announcements
    WIKI_ANNOUNCEMENTS.forEach(announcement => {
        wikiKnowledge.push({
            id: `WIKI-${announcement.id}`,
            question: `What is the announcement about ${announcement.title}?`,
            answer: announcement.content,
            source: `Company Announcement: ${announcement.title}`,
            quote: announcement.content.substring(0, 200) + (announcement.content.length > 200 ? '...' : ''),
            tags: ['announcement', 'news', 'company', 'wiki', ...extractKeywords(announcement.title)],
            accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
            documentId: announcement.id,
            documentType: 'announcement',
            expertAuthor: announcement.author + (announcement.authorDepartment ? ` - ${announcement.authorDepartment}` : ''),
            lastUpdated: announcement.date,
            successRate: 85 + Math.floor(Math.random() * 10), // 85-94%
            relatedDocuments: []
        });
    });

    // Convert Policies
    WIKI_POLICIES.forEach(policy => {
        wikiKnowledge.push({
            id: `WIKI-${policy.id}`,
            question: `What is the policy on ${policy.title}?`,
            answer: policy.summary,
            source: `Company Policy: ${policy.title}`,
            quote: policy.summary.substring(0, 200) + (policy.summary.length > 200 ? '...' : ''),
            tags: ['policy', policy.category.toLowerCase(), 'wiki', ...extractKeywords(policy.title)],
            accessRoles: ['CEO', 'Manager', 'Worker', 'Salesperson', 'QA Engineer', 'Super User', 'Technician', 'Engineer', 'Production Worker'],
            documentId: policy.id,
            documentType: 'policy',
            expertAuthor: policy.author + (policy.authorDepartment ? ` - ${policy.authorDepartment}` : ''),
            lastUpdated: policy.lastUpdated,
            successRate: 92 + Math.floor(Math.random() * 6), // 92-97%
            relatedDocuments: []
        });
    });

    return wikiKnowledge;
};

// Generate alternative questions for better matching
const generateAlternativeQuestions = (title: string, content: string): string[] => {
    const alternatives: string[] = [];

    if (title.includes('Philosophy')) {
        alternatives.push('What is our company philosophy?');
        alternatives.push('What are our core values?');
        alternatives.push('What is our management approach?');
    }

    if (title.includes('Technology') || title.includes('Technical')) {
        alternatives.push('How does this technology work?');
        alternatives.push('What are the technical specifications?');
        alternatives.push('Can you explain this technology?');
    }

    if (title.includes('Refrigerant') || title.includes('R-32')) {
        alternatives.push('What refrigerant do we use?');
        alternatives.push('What is R-32?');
        alternatives.push('Tell me about our refrigerant technology');
    }

    if (title.includes('Fusion') || title.includes('Plan')) {
        alternatives.push('What is our strategic plan?');
        alternatives.push('What are our business goals?');
        alternatives.push('What is our company strategy?');
    }

    return alternatives;
};

// Extract keywords from text
const extractKeywords = (text: string): string[] => {
    const commonWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must'];
    return text.toLowerCase()
        .split(/\s+/)
        .filter(word => word.length > 3 && !commonWords.includes(word))
        .slice(0, 5); // Limit to 5 keywords
};

// Get combined knowledge base (original + wiki content)
export const getCombinedKnowledgeBase = (): AIAssistantKnowledge[] => {
    return [...AI_ASSISTANT_KNOWLEDGE_BASE, ...convertWikiToKnowledge()];
};

// Enhanced AI Assistant that uses Ollama for more natural responses
export const generateAIResponse = async (
    query: string, 
    matchedKnowledge: AIAssistantKnowledge | null, 
    currentUser: User
): Promise<string> => {
    try {
        if (matchedKnowledge) {
            // Check access permissions
            if (!matchedKnowledge.accessRoles.includes(currentUser.role)) {
                return `I found some information related to your query, but access is restricted. This information is only available to roles: ${matchedKnowledge.accessRoles.join(', ')}. Please contact your manager for access.`;
            }

            // Use Ollama to generate a more natural response based on the knowledge
            const systemPrompt = `You are a professional AI assistant for AIT Systems company's internal knowledge system.

CONTEXT:
- User Query: "${query}"

KNOWLEDGE BASE MATCH:
- Source: ${matchedKnowledge.source}
- Content: ${matchedKnowledge.answer}
- Quote: "${matchedKnowledge.quote}"

INSTRUCTIONS:
1. Provide a direct, accurate answer based ONLY on the knowledge base information
2. Use professional, clear language
3. Structure your response with the main answer first
4. Include relevant details from the source content
5. Reference the source when appropriate
6. Keep the response concise and focused (1-2 paragraphs)
7. Do not add information not present in the knowledge base

Provide a helpful, professional response to the user's question.`;

            const response = await callOllamaChat(query, systemPrompt);
            
            // If Ollama fails, fall back to the original structured response
            if (!response || response.trim().length === 0) {
                return JSON.stringify({
                    summary: matchedKnowledge.answer,
                    source: matchedKnowledge.source,
                    quote: matchedKnowledge.quote,
                });
            }
            
            // Return the enhanced response, but also include structured data for the UI
            return JSON.stringify({
                summary: response.trim(),
                source: matchedKnowledge.source,
                quote: matchedKnowledge.quote,
            });
        } else {
            // No matching knowledge found - use Ollama to generate a helpful "not found" response
            const systemPrompt = `You are a professional AI assistant for AIT Systems company's internal knowledge system.

SITUATION:
- User Query: "${query}"
- No relevant information found in the company knowledge base

INSTRUCTIONS:
1. Politely explain that you couldn't find information about their specific question
2. Clarify that you can only provide answers based on company documents and policies
3. Suggest appropriate next steps (contact manager, HR, or specific department)
4. Maintain a helpful, professional tone
5. Keep the response brief (1 paragraph)
6. Offer to help with other questions that might be in your knowledge base

Provide a helpful response explaining the limitation and suggesting next steps.`;

            try {
                const response = await callOllamaChat(query, systemPrompt);
                return response.trim() || "I'm sorry, I couldn't find any information related to your question in our internal knowledge base. I can only provide answers based on company documents and policies.";
            } catch (error) {
                // Fallback if Ollama fails
                return "I'm sorry, I couldn't find any information related to your question in our internal knowledge base. I can only provide answers based on company documents and policies.";
            }
        }
    } catch (error) {
        console.error('Error generating AI response:', error);
        // Fallback to original logic if Ollama fails
        if (matchedKnowledge && matchedKnowledge.accessRoles.includes(currentUser.role)) {
            return JSON.stringify({
                summary: matchedKnowledge.answer,
                source: matchedKnowledge.source,
                quote: matchedKnowledge.quote,
            });
        } else if (matchedKnowledge) {
            return `I found some information related to your query, but access is restricted. This information is only available to roles: ${matchedKnowledge.accessRoles.join(', ')}. Please contact your manager for access.`;
        } else {
            return "I'm sorry, I couldn't find any information related to your question in our internal knowledge base. I can only provide answers based on company documents and policies.";
        }
    }
};

// Enhanced RAG search with multi-source integration and confidence scoring
export const findBestKnowledgeMatch = (query: string, knowledgeBase: AIAssistantKnowledge[]): AIAssistantKnowledge | null => {
    const queryWords = query.toLowerCase().split(/\s+/).filter(w => w.length > 2);
    let bestMatch = null;
    let maxScore = 0;

    knowledgeBase.forEach(item => {
        let currentScore = 0;
        const content = `${item.question} ${item.answer} ${item.tags.join(' ')}`.toLowerCase();

        // Exact phrase matching gets higher score
        if (content.includes(query.toLowerCase())) {
            currentScore += 15;
        }

        // Title matching gets bonus
        if (item.question.toLowerCase().includes(query.toLowerCase())) {
            currentScore += 12;
        }

        // Word matching with position weighting
        queryWords.forEach(word => {
            const wordCount = (content.match(new RegExp(word, 'g')) || []).length;
            currentScore += wordCount * 1.5; // Multiple occurrences get more weight

            // Bonus for word in question/title
            if (item.question.toLowerCase().includes(word)) {
                currentScore += 3;
            }
        });

        // Tag matching gets bonus points
        item.tags.forEach(tag => {
            if (queryWords.some(word => tag.toLowerCase().includes(word))) {
                currentScore += 2;
            }
        });

        // Expert author bonus
        if (item.expertAuthor) {
            currentScore += 1;
        }

        // Success rate bonus
        if (item.successRate && item.successRate > 80) {
            currentScore += 2;
        }

        // Recent update bonus
        if (item.lastUpdated) {
            const updateDate = new Date(item.lastUpdated);
            const monthsOld = (Date.now() - updateDate.getTime()) / (1000 * 60 * 60 * 24 * 30);
            if (monthsOld < 6) {
                currentScore += 2;
            } else if (monthsOld < 12) {
                currentScore += 1;
            }
        }

        // Wiki content bonus (since it's more comprehensive)
        if (item.id.startsWith('WIKI-')) {
            currentScore += 3;
        }

        // Document type bonuses
        if (item.documentType === 'knowledge_article') {
            currentScore += 2;
        } else if (item.documentType === 'policy') {
            currentScore += 1.5;
        }

        // Source quality bonus
        if (item.source && item.source.includes('Wiki')) {
            currentScore += 2;
        }

        if (currentScore > maxScore) {
            maxScore = currentScore;
            bestMatch = item;
        }
    });

    // Calculate confidence score with improved normalization
    if (bestMatch && maxScore > 3) {
        // Improved confidence calculation for better accuracy
        const normalizedScore = Math.min(maxScore / 25, 1); // Normalize to 0-1 with higher threshold
        bestMatch.confidenceScore = Math.max(0.6, normalizedScore); // Minimum 60% confidence for matches
        return bestMatch;
    }

    return null;
};

// Enhanced RAG response generation with multi-source support
export const generateEnhancedRAGResponse = async (
    query: string,
    knowledgeBase: AIAssistantKnowledge[],
    currentUser: User
): Promise<EnhancedRAGResponse> => {
    const startTime = Date.now();

    // Find multiple relevant sources
    const relevantSources = findMultipleKnowledgeMatches(query, knowledgeBase, 3);

    // Enhanced permission filtering with detailed access control
    const accessibleSources = relevantSources.filter(source =>
        source.accessRoles.includes(currentUser.role)
    );

    const restrictedSources = relevantSources.filter(source =>
        !source.accessRoles.includes(currentUser.role)
    );

    if (accessibleSources.length === 0) {
        // Provide detailed permission error with suggestions
        const availableRoles = [...new Set(relevantSources.flatMap(s => s.accessRoles))];
        const errorMessage = `Access restricted. Found ${relevantSources.length} relevant sources, but none are accessible to your role (${currentUser.role}).

Required roles for this information: ${availableRoles.join(', ')}

Please contact your manager or system administrator for access to this information.`;

        throw new Error(errorMessage);
    }

    // Convert to enhanced RAG sources with detailed metadata
    const ragSources: RAGSource[] = accessibleSources.map((source, index) => {
        // Calculate user experience relevance
        const userExperience = currentUser.experience || {};
        const categoryKey = getCategoryExperienceKey(source.tags);
        const userExperienceScore = categoryKey ? userExperience[categoryKey] || 0 : 0;

        return {
            document_id: source.documentId || source.id,
            relevance_score: Math.round((source.confidenceScore || (0.9 - index * 0.1)) * 100) / 100,
            expert_author: source.expertAuthor || 'System Generated',
            success_rate: `${source.successRate || Math.floor(Math.random() * 20) + 80}%`,
            last_updated: source.lastUpdated || new Date().toISOString().split('T')[0],
            document_excerpt: source.quote,
            document_type: source.documentType || 'knowledge_article',
            section: source.source,
            // Enhanced metadata
            document_url: `#/document/${source.documentId || source.id}`,
            page_reference: `Page ${Math.floor(Math.random() * 50) + 1}`,
            credibility_score: Math.round(((source.confidenceScore || 0.8) * 0.8 + Math.random() * 0.2) * 100) / 100,
            related_documents: source.relatedDocuments || [],
            // User experience integration
            user_experience_score: userExperienceScore,
            access_level: currentUser.role,
            permission_granted: true,
            restricted_count: restrictedSources.length
        };
    });

    // Generate enhanced response using primary source
    const primarySource = accessibleSources[0];
    const systemPrompt = `You are a professional AI assistant for AIT Systems company's internal knowledge system.

CONTEXT:
- User Role: ${currentUser.role} (${currentUser.department})
- User Query: "${query}"

PRIMARY SOURCE INFORMATION:
- Source: ${primarySource.source}
- Expert Author: ${primarySource.expertAuthor || 'System Generated'}
- Success Rate: ${primarySource.successRate || 'N/A'}%
- Last Updated: ${primarySource.lastUpdated || 'N/A'}

CONTENT TO USE:
${primarySource.answer}

SUPPORTING QUOTE:
"${primarySource.quote}"

INSTRUCTIONS:
1. Provide a direct, accurate answer based ONLY on the source information provided
2. Use professional, clear language appropriate for a ${currentUser.role}
3. Include specific details from the source content
4. Reference the source and expert author when relevant
5. If the source doesn't fully answer the question, acknowledge the limitation
6. Keep the response focused and concise (2-3 paragraphs maximum)
7. Do not add information not present in the source material

Additional context: ${accessibleSources.length - 1} related sources are also available for this topic.`;

    try {
        const response = await callOllamaChat(query, systemPrompt);

        const processingTime = Date.now() - startTime;

        // Calculate enhanced metrics
        const confidenceScore = Math.round((primarySource.confidenceScore || 0.85) * 100) / 100;
        const alternativeSolutions = accessibleSources.length - 1;

        return {
            answer: response.trim(),
            sources: ragSources,
            confidence_score: confidenceScore,
            alternative_solutions: alternativeSolutions,
            query_processing_time: processingTime,
            knowledge_graph_connections: primarySource.relatedDocuments || [],
            expert_recommendations: generateExpertRecommendations(primarySource),
            // Enhanced metadata
            search_strategy: determineSearchStrategy(query),
            quality_indicators: {
                source_diversity: accessibleSources.length > 1,
                expert_validated: accessibleSources.some(s => s.expertAuthor),
                recent_information: accessibleSources.some(s => isRecentDocument(s.lastUpdated)),
                high_success_rate: accessibleSources.some(s => (s.successRate || 0) > 90)
            },
            related_queries: generateRelatedQueries(query, primarySource),
            synthesis_method: accessibleSources.length > 1 ? 'multi_source' : 'single_source',
            document_coverage: Math.round((accessibleSources.length / Math.min(relevantSources.length, 5)) * 100)
        };
    } catch (error) {
        console.error('Error generating enhanced RAG response:', error);
        throw error;
    }
};

// Helper function to map category tags to user experience keys
const getCategoryExperienceKey = (tags: string[]): keyof User['experience'] | null => {
    const tagMap: Record<string, keyof User['experience']> = {
        'it': 'itSupport',
        'technical': 'itSupport',
        'system': 'itSupport',
        'finance': 'finance',
        'budget': 'finance',
        'cost': 'finance',
        'admin': 'adminRequest',
        'request': 'adminRequest',
        'procedure': 'adminRequest',
        'marketing': 'marketing',
        'sales': 'marketing',
        'promotion': 'marketing',
        'hr': 'humanResources',
        'human resources': 'humanResources',
        'employee': 'humanResources',
        'quality': 'qualityAssurance',
        'qa': 'qualityAssurance',
        'testing': 'qualityAssurance',
        'operations': 'operations',
        'production': 'operations',
        'maintenance': 'operations',
        'rnd': 'rnd',
        'research': 'rnd',
        'development': 'rnd'
    };

    for (const tag of tags) {
        const key = tagMap[tag.toLowerCase()];
        if (key) return key;
    }
    return null;
};

// Find multiple knowledge matches for multi-source RAG
const findMultipleKnowledgeMatches = (
    query: string,
    knowledgeBase: AIAssistantKnowledge[],
    maxResults: number = 3
): AIAssistantKnowledge[] => {
    const queryWords = query.toLowerCase().split(/\s+/).filter(w => w.length > 2);
    const scoredItems: { item: AIAssistantKnowledge; score: number }[] = [];

    knowledgeBase.forEach(item => {
        let currentScore = 0;
        const content = `${item.question} ${item.answer} ${item.tags.join(' ')}`.toLowerCase();

        // Exact phrase matching
        if (content.includes(query.toLowerCase())) {
            currentScore += 10;
        }

        // Word matching
        queryWords.forEach(word => {
            if (content.includes(word)) {
                currentScore += 1;
            }
        });

        // Tag matching
        item.tags.forEach(tag => {
            if (queryWords.some(word => tag.toLowerCase().includes(word))) {
                currentScore += 2;
            }
        });

        // Quality bonuses
        if (item.expertAuthor) currentScore += 1;
        if (item.successRate && item.successRate > 80) currentScore += 2;

        if (currentScore > 2) {
            item.confidenceScore = Math.min(currentScore / 15, 1);
            scoredItems.push({ item, score: currentScore });
        }
    });

    // Sort by score and return top results
    return scoredItems
        .sort((a, b) => b.score - a.score)
        .slice(0, maxResults)
        .map(scored => scored.item);
};

// Generate expert recommendations based on source
const generateExpertRecommendations = (source: AIAssistantKnowledge): ExpertRecommendation[] => {
    if (!source.expertAuthor) return [];

    return [{
        expert_id: `expert-${source.id}`,
        expert_name: source.expertAuthor,
        recommendation: `For detailed guidance on this topic, consult with ${source.expertAuthor}`,
        contact_info: `${source.expertAuthor.toLowerCase().replace(' ', '.')}@company.com`
    }];
};

// RAG Performance Metrics Tracking
class RAGMetricsTracker {
    private metrics: RAGMetrics = {
        source_accuracy_rate: 0,
        response_relevance_score: 0,
        knowledge_coverage: 0,
        expert_validation_rate: 0,
        query_response_time: 0,
        source_retrieval_speed: 0,
        user_satisfaction_score: 0,
        system_uptime: 100
    };

    private queryHistory: Array<{
        query: string;
        responseTime: number;
        sourcesFound: number;
        userRating?: number;
        timestamp: number;
    }> = [];

    recordQuery(query: string, responseTime: number, sourcesFound: number) {
        this.queryHistory.push({
            query,
            responseTime,
            sourcesFound,
            timestamp: Date.now()
        });

        // Update metrics
        this.updateMetrics();
    }

    recordUserRating(queryIndex: number, rating: number) {
        if (this.queryHistory[queryIndex]) {
            this.queryHistory[queryIndex].userRating = rating;
            this.updateMetrics();
        }
    }

    private updateMetrics() {
        const recentQueries = this.queryHistory.slice(-100); // Last 100 queries

        if (recentQueries.length > 0) {
            this.metrics.query_response_time = recentQueries.reduce((sum, q) => sum + q.responseTime, 0) / recentQueries.length;
            this.metrics.knowledge_coverage = (recentQueries.filter(q => q.sourcesFound > 0).length / recentQueries.length) * 100;

            const ratedQueries = recentQueries.filter(q => q.userRating !== undefined);
            if (ratedQueries.length > 0) {
                this.metrics.user_satisfaction_score = ratedQueries.reduce((sum, q) => sum + (q.userRating || 0), 0) / ratedQueries.length;
            }
        }
    }

    getMetrics(): RAGMetrics {
        return { ...this.metrics };
    }

    getQueryHistory() {
        return [...this.queryHistory];
    }
}

// Global metrics tracker instance
export const ragMetricsTracker = new RAGMetricsTracker();

// Enhanced source verification
export const verifyRAGSources = async (sources: RAGSource[]): Promise<boolean> => {
    // Simulate source verification process
    return new Promise((resolve) => {
        setTimeout(() => {
            // In a real implementation, this would verify document authenticity,
            // check access permissions, and validate source credibility
            const allSourcesValid = sources.every(source =>
                source.document_id &&
                source.relevance_score > 0.3 &&
                source.document_excerpt.length > 10
            );
            resolve(allSourcesValid);
        }, 100);
    });
};

// Document intelligence for processing uploaded files
export const processDocumentIntelligence = async (fileContent: string, fileName: string): Promise<{
    extractedData: Record<string, any>;
    confidence: number;
    suggestedFields: string[];
}> => {
    // Simulate document processing
    return new Promise((resolve) => {
        setTimeout(() => {
            const extractedData: Record<string, any> = {};
            const suggestedFields: string[] = [];

            // Simple extraction logic (in real implementation, use OCR/NLP)
            if (fileName.toLowerCase().includes('maintenance')) {
                extractedData.type = 'maintenance_request';
                extractedData.priority = 'medium';
                suggestedFields.push('equipment_id', 'issue_description', 'urgency');
            } else if (fileName.toLowerCase().includes('safety')) {
                extractedData.type = 'safety_incident';
                extractedData.priority = 'high';
                suggestedFields.push('incident_date', 'location', 'description');
            }

            resolve({
                extractedData,
                confidence: 0.85,
                suggestedFields
            });
        }, 500);
    });
};

// Test Ollama connection
export const testOllamaConnection = async (): Promise<boolean> => {
    try {
        const response = await axios.get(`${OLLAMA_CONFIG.base_url}/api/tags`, {
            timeout: 5000
        });

        // Check if llama3.1:8b model is available
        const models = response.data.models || [];
        const hasModel = models.some((model: any) => model.name.includes('llama3.1:8b'));

        if (!hasModel) {
            console.warn('llama3.1:8b model not found in Ollama. Available models:', models.map((m: any) => m.name));
        }

        return true;
    } catch (error) {
        console.error('Ollama connection test failed:', error);
        return false;
    }
};

// Determine search strategy based on query characteristics
const determineSearchStrategy = (query: string): string => {
    const queryLower = query.toLowerCase();
    if (queryLower.includes('how to') || queryLower.includes('procedure')) return 'procedural_search';
    if (queryLower.includes('what is') || queryLower.includes('define')) return 'definitional_search';
    if (queryLower.includes('troubleshoot') || queryLower.includes('problem')) return 'diagnostic_search';
    if (queryLower.includes('best practice') || queryLower.includes('recommend')) return 'advisory_search';
    return 'general_search';
};

// Check if document is recent (within last 6 months)
const isRecentDocument = (lastUpdated?: string): boolean => {
    if (!lastUpdated) return false;
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    return new Date(lastUpdated) > sixMonthsAgo;
};

// Generate related queries based on current query and knowledge
const generateRelatedQueries = (query: string, knowledge: AIAssistantKnowledge): string[] => {
    const relatedQueries: string[] = [];

    // Generate queries based on tags
    knowledge.tags.forEach(tag => {
        if (!query.toLowerCase().includes(tag.toLowerCase())) {
            relatedQueries.push(`What is ${tag}?`);
            relatedQueries.push(`How to use ${tag}?`);
        }
    });

    // Generate procedural variations
    if (!query.toLowerCase().includes('how to')) {
        relatedQueries.push(`How to ${query.toLowerCase().replace(/what is|define/, '').trim()}?`);
    }

    return relatedQueries.slice(0, 3); // Limit to 3 related queries
};
