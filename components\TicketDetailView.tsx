
import React, { useState, useEffect, useContext } from 'react';
import type { Ticket, User, Activity, TicketWorkPlan, TicketStatus, CompletionReport } from '../types';
import { AppContext } from './AppContext';
import { generateTicketWorkPlan, suggestAssignees, generateCompletionReport } from '../services/ollamaService';
import { PencilIcon, PaperclipIcon, ArrowUturnLeftIcon, BotIcon, CheckIcon, XMarkIcon, ChatBubbleLeftRightIcon, ArrowPathIcon, ExclamationTriangleIcon, ClipboardDocumentCheckIcon } from './icons/Icons';

// --- Reusable Components ---
const StatusBadge: React.FC<{ status: TicketStatus }> = ({ status }) => {
  const statusStyles: Record<TicketStatus, string> = {
    Received: 'bg-blue-100 text-blue-800',
    'In Progress': 'bg-violet-100 text-violet-800',
    'Waiting Feedback': 'bg-orange-100 text-orange-800',
    Done: 'bg-green-100 text-green-800',
    Reopened: 'bg-pink-100 text-pink-800',
    Canceled: 'bg-gray-200 text-gray-700 line-through',
    Closed: 'bg-gray-200 text-gray-700',
  };
  return <span className={`px-2.5 py-1 text-xs font-semibold rounded-full ${statusStyles[status]}`}>{status}</span>;
};

const UrgencyDisplay: React.FC<{ urgency: Ticket['urgency'] }> = ({ urgency }) => {
    const urgencyColors: Record<Ticket['urgency'], string> = {
        High: "text-red-600",
        Medium: "text-orange-600",
        Low: "text-green-600"
    };
    return <span className={`${urgencyColors[urgency]}`}>{urgency}</span>
}

export const AssigneeList: React.FC<{ userIds: string[], title: string }> = ({ userIds, title }) => {
    const { users } = useContext(AppContext);
    return (
        <div>
            <h4 className="text-sm font-semibold text-gray-700 mb-2">{title}</h4>
            {userIds.length > 0 ? (
                <div className="flex flex-wrap gap-3">
                    {userIds.map(id => {
                        const user = users.find(u => u.id === id);
                        return user ? <div key={id} className="flex items-center gap-2 bg-gray-100 p-2 rounded-lg">
                            <div className="h-7 w-7 rounded-full bg-gray-300 flex items-center justify-center text-xs font-bold">{user.avatar}</div>
                            <div>
                                <p className="text-sm font-semibold text-gray-800">{user.name}</p>
                                <p className="text-xs text-gray-500">{user.role} ({user.department})</p>
                            </div>
                        </div> : null;
                    })}
                </div>
            ) : <p className="text-sm text-gray-500">No assignees yet.</p>}
        </div>
    );
};

export const AISummaryActivity: React.FC<{summary: TicketWorkPlan, timestamp: string}> = ({ summary, timestamp }) => (
    <div className="flex items-start gap-4">
        <div className="w-8 h-8 rounded-full bg-violet-500 flex items-center justify-center flex-shrink-0"><BotIcon className="w-5 h-5 text-white" /></div>
        <div className="flex-1 bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-3">
                <h4 className="font-semibold text-gray-900">AI Generated Work Plan</h4>
                <span className="text-xs text-gray-500">{timestamp}</span>
            </div>
            <div className="prose prose-sm max-w-none text-gray-600 space-y-4">
                {summary.overview && <div><h5 className="font-bold text-gray-800">Overview</h5><p>{summary.overview}</p></div>}
                {summary.resolution && <div><h5 className="font-bold text-gray-800">Suggested Resolution</h5><p>{summary.resolution}</p></div>}
                {summary.workBreakdown && summary.workBreakdown.length > 0 && <div>
                    <h5 className="font-bold text-gray-800">Work Breakdown</h5>
                    <ul className="space-y-2">
                        {summary.workBreakdown.map((item, index) => (
                            <li key={index} className="flex items-center gap-2">
                                <div className="w-4 h-4 rounded-sm bg-gray-200 flex items-center justify-center"><CheckIcon className="w-3 h-3 text-gray-500" /></div>
                                <span>{item}</span>
                            </li>
                        ))}
                    </ul>
                </div>}
            </div>
        </div>
    </div>
);

const AICompletionReportActivity: React.FC<{ report: CompletionReport, timestamp: string }> = ({ report, timestamp }) => (
    <div className="flex items-start gap-4">
        <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center flex-shrink-0">
            <ClipboardDocumentCheckIcon className="w-5 h-5 text-white" />
        </div>
        <div className="flex-1 bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-3">
                <h4 className="font-semibold text-gray-900">AI Completion Report</h4>
                <span className="text-xs text-gray-500">{timestamp}</span>
            </div>
            <div className="prose prose-sm max-w-none text-gray-600 space-y-4">
                {report.summaryOfResolution && <div><h5 className="font-bold text-gray-800">Summary of Resolution</h5><p>{report.summaryOfResolution}</p></div>}
                {report.identifiedRootCause && <div><h5 className="font-bold text-gray-800">Identified Root Cause</h5><p>{report.identifiedRootCause}</p></div>}
                {report.nextStepsOrRecommendations && report.nextStepsOrRecommendations.length > 0 && <div>
                    <h5 className="font-bold text-gray-800">Next Steps & Recommendations</h5>
                    <ul className="space-y-2">
                        {report.nextStepsOrRecommendations.map((item, index) => (
                            <li key={index} className="flex items-center gap-2">
                                <div className="w-4 h-4 rounded-sm bg-gray-200 flex items-center justify-center"><CheckIcon className="w-3 h-3 text-gray-500" /></div>
                                <span>{item}</span>
                            </li>
                        ))}
                    </ul>
                </div>}
            </div>
        </div>
    </div>
);


// --- Main View Component ---
const TicketDetailView: React.FC<{ 
    ticketId: string;
    onBack: () => void;
}> = ({ ticketId, onBack }) => {
    const { tickets, users, updateTicket, currentUser } = useContext(AppContext);
    const ticket = tickets.find(t => t.id === ticketId) || null;
    
    const [isRegenerating, setIsRegenerating] = useState(false);
    const [isReassigning, setIsReassigning] = useState(false);
    const [isClosingTicket, setIsClosingTicket] = useState(false);
    
    const [confirmationModal, setConfirmationModal] = useState<{isOpen: boolean, userToAssign: User | null}>({isOpen: false, userToAssign: null});

    const isAssignee = ticket?.assignees.includes(currentUser.id);
    const canTakeAction = isAssignee || currentUser.role === 'Super User';
    const isClosed = ticket?.status === 'Done' || ticket?.status === 'Closed' || ticket?.status === 'Canceled';


    const handleAssign = (user: User) => {
        if (!ticket) return;
        if (ticket.assignees.includes(user.id)) {
            updateTicket({ ...ticket, assignees: ticket.assignees.filter(id => id !== user.id) });
            return;
        }
        if (user.status !== 'Online') {
            setConfirmationModal({ isOpen: true, userToAssign: user });
            return;
        }
        updateTicket({ ...ticket, assignees: [...ticket.assignees, user.id] });
    };
    
    const confirmAssignment = () => {
        if (!ticket || !confirmationModal.userToAssign) return;
        updateTicket({ ...ticket, assignees: [...ticket.assignees, confirmationModal.userToAssign.id] });
        setConfirmationModal({ isOpen: false, userToAssign: null });
    }

    const handleRegeneratePlan = async () => {
        if (!ticket) return;
        setIsRegenerating(true);
        try {
            const workPlan = await generateTicketWorkPlan(ticket.title, ticket.description);
            const newActivity: Activity = {
                 id: `act-${Date.now()}`,
                 type: 'AI_SUMMARY',
                 timestamp: new Date().toLocaleString(),
                 content: JSON.stringify(workPlan),
                 user: { id: 'ai', name: 'AI Assistant', avatar: 'B' }
            };
            updateTicket({ ...ticket, activity: [newActivity, ...ticket.activity] });
        } catch (error) { console.error("Failed to regenerate plan", error); } finally { setIsRegenerating(false); }
    };
    
    const handleSuggestAssignees = async () => {
        if (!ticket) return;
        setIsReassigning(true);
        try {
            const onlineUsers = users.filter(u => u.status === 'Online');
            const assigneeIds = await suggestAssignees({ title: ticket.title, description: ticket.description, category: ticket.category }, onlineUsers);
            updateTicket({ ...ticket, assignees: assigneeIds });
        } catch (error) { console.error("Failed to suggest assignees", error); } finally { setIsReassigning(false); }
    };

    const handleMarkAsDone = async () => {
        if (!ticket) return;
        setIsClosingTicket(true);
        try {
            const report = await generateCompletionReport(ticket.title, ticket.description);
            const newActivity: Activity = {
                 id: `act-${Date.now()}`,
                 type: 'AI_COMPLETION_REPORT',
                 timestamp: new Date().toLocaleString(),
                 content: JSON.stringify(report),
                 user: { id: 'ai', name: 'AI Assistant', avatar: 'B' }
            };
            updateTicket({ ...ticket, status: 'Done', activity: [newActivity, ...ticket.activity] });
        } catch (error) {
            console.error("Failed to generate completion report", error);
        } finally {
            setIsClosingTicket(false);
        }
    };


    if (!ticket) {
        return <div className="p-8 text-center">Ticket not found.</div>
    }

    return (
    <>
    <div className="flex-1 flex bg-gray-50 h-full overflow-hidden">
        <div className="flex-[2] overflow-y-auto p-8">
             <button onClick={onBack} className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 mb-6"><ArrowUturnLeftIcon className="w-4 h-4"/>Back to List</button>
            <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex justify-between items-start mb-4">
                    <div>
                        <span className="text-sm text-gray-500">{ticket.id}</span>
                        <h2 className="text-2xl font-bold text-gray-900 mt-1">{ticket.title}</h2>
                        <div className="flex items-center gap-2 mt-2">
                           <StatusBadge status={ticket.status} /><span className="text-gray-400">&middot;</span>
                           <span className="font-medium text-gray-700">{ticket.category}</span><span className="text-gray-400">&middot;</span>
                           <UrgencyDisplay urgency={ticket.urgency} />
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        {canTakeAction && !isClosed && (
                            <button 
                                onClick={handleMarkAsDone}
                                disabled={isClosingTicket} 
                                className="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium bg-green-600 hover:bg-green-700 text-white transition-colors disabled:bg-green-300 disabled:cursor-wait">
                                <CheckIcon className={`w-4 h-4 ${isClosingTicket ? 'animate-spin' : ''}`} />
                                {isClosingTicket ? 'Generating Report...' : 'Mark as Done'}
                            </button>
                        )}
                        <button onClick={handleRegeneratePlan} disabled={isRegenerating || isClosingTicket} className="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium bg-violet-600 hover:bg-violet-700 text-white transition-colors disabled:bg-violet-300 disabled:cursor-wait">
                            <ArrowPathIcon className={`w-4 h-4 ${isRegenerating ? 'animate-spin' : ''}`} />
                            {isRegenerating ? 'Regenerating...' : 'Regenerate Plan'}
                        </button>
                    </div>
                </div>
                <p className="text-gray-600 leading-relaxed mb-6 whitespace-pre-wrap">{ticket.description}</p>
                <AssigneeList userIds={ticket.assignees} title="Assignees" />
                <div className="mt-6">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">Attachments ({ticket.attachments?.length || 0})</h4>
                     {ticket.attachments && ticket.attachments.length > 0 ? ticket.attachments.map((att, idx) => (
                        <div key={idx} className="border border-gray-200 rounded-lg p-3 flex items-center gap-3 text-sm text-blue-600 hover:bg-gray-100 cursor-pointer">
                            <PaperclipIcon className="w-5 h-5" /><span>{att.name}</span><span className="text-xs text-gray-500 ml-auto">{att.size}</span>
                        </div>
                     )) : <p className="text-sm text-gray-500">No attachments.</p>}
                </div>
            </div>

            <div className="mt-8">
                 <div className="flex items-center gap-4 border-b border-gray-200 mb-4"><button className="py-3 px-1 text-sm font-semibold text-blue-600 border-b-2 border-blue-600">Activity</button></div>
                <div className="space-y-6">
                 {ticket.activity.map(act => {
                     if (act.type === 'AI_SUMMARY') {
                         const summary: TicketWorkPlan | null = act.content ? JSON.parse(act.content) : null;
                         if (!summary) return null;
                         return <AISummaryActivity key={act.id} summary={summary} timestamp={act.timestamp} />;
                     }
                      if (act.type === 'AI_COMPLETION_REPORT') {
                         const report: CompletionReport | null = act.content ? JSON.parse(act.content) : null;
                         if (!report) return null;
                         return <AICompletionReportActivity key={act.id} report={report} timestamp={act.timestamp} />;
                     }
                     if (act.type === 'CREATED') {
                         const user = act.user;
                         return <div key={act.id} className="flex items-center gap-4"><div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0 text-lg font-bold text-gray-600">{user ? user.avatar : 'S'}</div><div className="flex-1"><p className="text-sm text-gray-700">{act.content}</p><p className="text-xs text-gray-500">{act.timestamp}</p></div></div>;
                     }
                     return null;
                 })}
                </div>
            </div>
        </div>
        <aside className="w-96 border-l border-gray-200 p-6 overflow-y-auto bg-white">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Assign Ticket</h3><p className="text-sm text-gray-500 mb-4">Add or remove assignees for this ticket.</p>
            <button onClick={handleSuggestAssignees} disabled={isReassigning || isClosingTicket} className="w-full flex items-center justify-center gap-2 mb-4 px-3 py-1.5 rounded-md text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white transition-colors disabled:bg-blue-300 disabled:cursor-wait">
                <BotIcon className="w-4 h-4" />{isReassigning ? 'Thinking...' : 'AI Suggest Assignees'}
            </button>
            <div className="space-y-3">
                {users.filter(u => u.role !== 'Super User').map(user => {
                    const statusColor = user.status === 'Online' ? 'bg-green-500' : user.status === 'On Leave' ? 'bg-gray-400' : 'bg-yellow-400';
                    return (
                    <div key={user.id} className="bg-gray-50 border border-gray-200 rounded-lg p-3 flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center font-bold text-lg text-gray-600 relative">
                            {user.avatar}
                            <span className={`absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full ${statusColor} ring-2 ring-gray-50`}></span>
                        </div>
                        <div className="flex-1"><p className="font-semibold text-gray-800">{user.name}</p><p className="text-xs text-gray-500">{user.role}</p></div>
                         <button onClick={() => handleAssign(user)} disabled={isClosingTicket} className={`px-4 py-1.5 text-sm font-semibold rounded-md transition-colors ${ticket.assignees.includes(user.id) ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'} disabled:bg-gray-300 disabled:cursor-not-allowed`}>
                            {ticket.assignees.includes(user.id) ? 'Unassign' : 'Assign'}
                        </button>
                    </div>
                )})}
            </div>
        </aside>
    </div>
    
    {confirmationModal.isOpen && (
         <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full border border-gray-200">
                <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center"><ExclamationTriangleIcon className="w-6 h-6 text-yellow-500" /></div>
                    <div><h3 className="text-lg font-bold text-gray-900">Confirm Assignment</h3></div>
                </div>
                <p className="text-gray-600 mb-6"> {confirmationModal.userToAssign?.name} is currently <span className="font-semibold text-yellow-600">{confirmationModal.userToAssign?.status}</span>. Assigning this ticket may result in delays. Are you sure you want to proceed?</p>
                <div className="flex justify-end gap-4">
                    <button onClick={() => setConfirmationModal({isOpen: false, userToAssign: null})} className="px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg">Cancel</button>
                    <button onClick={confirmAssignment} className="px-4 py-2 text-sm font-semibold text-white bg-yellow-500 hover:bg-yellow-600 rounded-lg">Assign Anyway</button>
                </div>
            </div>
        </div>
    )}
    </>
    )
};

export default TicketDetailView;