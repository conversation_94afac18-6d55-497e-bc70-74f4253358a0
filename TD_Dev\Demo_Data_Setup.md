# AIT Systems 演示数据准备指南

## 🎯 演示数据概览

本文档详细说明了客户演示所需的测试数据、用户账户设置和场景配置，确保演示过程流畅且具有说服力。

---

## 👥 演示用户账户设置

### 主要演示账户
1. **Demo Admin (Super User)**
   - 用户名: <EMAIL>
   - 角色: Super User
   - 权限: 完整系统访问权限
   - 用途: 主要演示账户，展示所有功能

2. **<PERSON> (IT Manager)**
   - 用户名: <EMAIL>
   - 角色: Manager
   - 部门: IT
   - 专长: IT支持(96%), 网络管理(89%), 系统维护(92%)
   - 用途: 展示技术管理场景

3. **<PERSON> (Operations Manager)**
   - 用户名: <EMAIL>
   - 角色: Manager
   - 部门: Production
   - 专长: 设备维护(94%), 生产管理(91%), 质量控制(88%)
   - 用途: 展示运营管理场景

4. **<PERSON> (IT Support)**
   - 用户名: <EMAIL>
   - 角色: Technician
   - 部门: IT
   - 专长: 技术支持(87%), 硬件维修(85%)
   - 用途: 展示一线技术人员视角

5. **<PERSON> (CEO)**
   - 用户名: <EMAIL>
   - 角色: CEO
   - 权限: 高级管理权限
   - 用途: 展示高层管理视角和权限差异

---

## 🎫 工单演示数据

### 预设工单场景

#### 场景1: IT支持工单 (演示用)
```
Title: 服务器性能优化需求
Category: IT Support
Priority: High
Description: 生产环境服务器响应缓慢，影响生产效率，需要进行性能分析和优化
Department: Production
Status: New
Expected Assignee: David Lee (96% IT支持成功率)
```

#### 场景2: 设备维护工单
```
Title: HVAC系统定期保养
Category: Maintenance
Priority: Medium
Description: 空调系统需要进行季度保养，包括滤网清洁、制冷剂检查等
Department: Facilities
Status: In Progress
Assignee: Bob Smith
```

#### 场景3: 财务报销工单
```
Title: 差旅费用报销申请
Category: Finance
Priority: Low
Description: 客户拜访差旅费用报销，包含交通、住宿、餐饮费用
Department: Sales
Status: Completed
```

### 工单统计数据
- **总工单数**: 156
- **本月新增**: 23
- **处理中**: 8
- **已完成**: 145
- **平均处理时间**: 2.3天
- **客户满意度**: 94%

---

## 🤖 AI Assistant 测试问题库

### 基础功能测试问题

#### 高置信度问题 (90%+)
1. **"How do I reset my password?"**
   - 预期置信度: 95%
   - 回答来源: IT Security Policy
   - 展示要点: 标准流程、安全要求

2. **"What are the working hours?"**
   - 预期置信度: 97%
   - 回答来源: HR Policy
   - 展示要点: 灵活工作时间、加班政策

3. **"How do I submit an expense report?"**
   - 预期置信度: 88%
   - 回答来源: Finance Policy
   - 展示要点: 流程步骤、所需材料

#### Wiki知识测试问题

4. **"What is our company philosophy?"**
   - 预期置信度: 95%
   - 回答来源: Wiki Knowledge Article
   - 展示要点: 人本管理理念、企业文化

5. **"What is inverter technology?"**
   - 预期置信度: 93%
   - 回答来源: Wiki Technical Article
   - 展示要点: 技术原理、应用优势

6. **"What is the Fusion 25 plan?"**
   - 预期置信度: 91%
   - 回答来源: Wiki Strategic Document
   - 展示要点: 战略目标、实施路径

#### 技术专业问题

7. **"How do I interpret fault codes on the maintenance dashboard?"**
   - 预期置信度: 88%
   - 回答来源: Maintenance Guide
   - 展示要点: 代码格式、优先级分类

8. **"What are the safety protocols for refrigerants?"**
   - 预期置信度: 96%
   - 回答来源: Safety Manual
   - 展示要点: 安全要求、操作规范

### 权限测试场景

#### 不同角色相同问题测试
**问题**: "What is our financial performance this quarter?"

- **CEO角色**: 显示详细财务数据和分析
- **Manager角色**: 显示部门相关财务信息
- **Worker角色**: 显示"权限不足"提示

#### 敏感信息访问测试
**问题**: "What is the salary structure?"

- **HR Manager**: 显示完整薪酬体系
- **普通员工**: 显示权限限制信息

---

## 📊 设备监控演示数据

### 设备状态设置

#### 正常运行设备 (绿色)
1. **HVAC-002**: 运行正常，效率98%
2. **PROD-001**: 生产设备，运行稳定
3. **NET-001**: 网络设备，连接正常

#### 需要关注设备 (黄色)
1. **HVAC-001**: 效率下降至85%，建议维护
2. **PROD-003**: 振动异常，需要检查
3. **POWER-002**: 负载较高，需要监控

#### 故障设备 (红色)
1. **HVAC-003**: 制冷剂泄漏，需要紧急维修
2. **PROD-002**: 传感器故障，已停机
3. **NET-003**: 网络中断，影响通信

### 预测分析数据

#### HVAC-001 预测分析结果
```
设备ID: HVAC-001
当前状态: 需要关注
故障概率: 85% (未来7天)
预计维护成本: ¥15,000
建议维护时间: 3天内
维护类型: 预防性维护
预期效果: 延长设备寿命6个月
```

#### 系统级分析结果
```
总设备数: 24台
健康设备: 18台 (75%)
需要关注: 4台 (17%)
故障设备: 2台 (8%)
预计月度维护成本: ¥45,000
通过预测维护可节约: ¥13,500 (30%)
```

---

## 📈 仪表板数据配置

### 主仪表板KPI指标
1. **工单处理效率**: 94% (目标: 90%)
2. **设备正常运行率**: 92% (目标: 95%)
3. **客户满意度**: 4.7/5.0
4. **平均响应时间**: 2.1小时 (目标: 4小时)
5. **成本节约**: ¥125,000/月

### 趋势图数据
- **工单量趋势**: 过去6个月稳步增长
- **处理时间趋势**: 持续优化，平均时间减少30%
- **设备健康度**: 整体稳定，个别设备需要关注
- **用户活跃度**: 日活跃用户85%，周活跃用户98%

---

## 🏢 组织架构演示数据

### 部门结构
```
AIT Systems
├── Executive (CEO: Alice Johnson)
├── Production (Manager: Bob Smith)
│   ├── Production Workers (5人)
│   └── QA Engineers (3人)
├── IT (Manager: David Lee)
│   ├── IT Support (Eve Williams等3人)
│   └── System Admins (2人)
├── Sales (Manager: 待定)
│   └── Sales Representatives (4人)
└── R&D (Manager: Rachel Silver)
    ├── Engineers (6人)
    └── Technicians (4人)
```

### 员工技能数据示例

#### David Lee (IT Manager)
```
技能专长:
- IT支持: 96% (处理156个工单，成功率96%)
- 网络管理: 89% (处理45个网络问题)
- 系统维护: 92% (负责12个系统)
- 项目管理: 87% (完成8个IT项目)

工作负载: 中等 (当前处理3个工单)
可用性: 立即可用
地理位置: 总部
```

#### Bob Smith (Operations Manager)
```
技能专长:
- 设备维护: 94% (维护23台设备)
- 生产管理: 91% (管理生产线效率)
- 质量控制: 88% (质量检查通过率)
- 团队管理: 93% (管理15人团队)

工作负载: 高 (当前处理5个工单)
可用性: 2小时后可用
地理位置: 工厂A
```

---

## 🎯 演示场景脚本

### 场景1: 紧急IT支持
**背景**: 生产环境服务器出现性能问题
**演示流程**:
1. 创建高优先级IT支持工单
2. 系统智能推荐David Lee (96%成功率)
3. David Lee接收并开始处理
4. 展示实时状态更新和协作

### 场景2: 预测性维护
**背景**: HVAC-001设备效率下降
**演示流程**:
1. 查看设备监控仪表板
2. 发现HVAC-001状态异常
3. 运行预测分析
4. 展示维护建议和成本节约

### 场景3: 知识查询
**背景**: 新员工需要了解公司政策
**演示流程**:
1. 使用AI助手查询工作时间政策
2. 展示高置信度回答和来源
3. 切换角色展示权限差异
4. 查询技术问题展示专业能力

---

## ✅ 演示前检查清单

### 数据检查
- [ ] 所有用户账户可正常登录
- [ ] 工单数据完整且状态正确
- [ ] 设备监控数据实时更新
- [ ] AI助手回答准确且置信度合理
- [ ] 组织架构数据完整

### 功能检查
- [ ] Smart Form创建和提交正常
- [ ] 工单分配算法工作正确
- [ ] AI助手响应速度正常
- [ ] 预测分析功能正常
- [ ] 权限控制生效

### 界面检查
- [ ] 所有页面加载正常
- [ ] 图表和可视化显示正确
- [ ] 响应式设计在不同屏幕尺寸下正常
- [ ] 无明显的UI错误或异常

### 性能检查
- [ ] 页面加载速度正常 (<3秒)
- [ ] AI助手响应时间合理 (<10秒)
- [ ] 数据查询和更新及时
- [ ] 系统整体运行稳定

---

*数据准备说明：建议在演示前24小时完成所有数据准备和系统检查，确保演示过程中不会出现数据或功能异常。如有问题，请及时联系技术团队进行修复。*
