
import axios from 'axios';
import type { AnalyzedTicketInfo, TicketCategory, TicketWorkPlan, TicketUrgency, User, FormField, CompletionReport } from '../types';

// Ollama Configuration
const OLLAMA_CONFIG = {
    base_url: 'http://localhost:11434',
    model: 'llama3.1:8b',
    api_key: null, // No authentication needed
    timeout: 30000,
};

// Helper function to call Ollama API
const callOllama = async (prompt: string, systemPrompt?: string): Promise<string> => {
    try {
        const response = await axios.post(`${OLLAMA_CONFIG.base_url}/api/generate`, {
            model: OLLAMA_CONFIG.model,
            prompt: systemPrompt ? `${systemPrompt}\n\nUser: ${prompt}` : prompt,
            stream: false,
            options: {
                temperature: 0.7,
                top_p: 0.9,
            }
        }, {
            timeout: OLLAMA_CONFIG.timeout,
            headers: {
                'Content-Type': 'application/json',
            }
        });

        return response.data.response || '';
    } catch (error) {
        console.error('Error calling Ollama API:', error);
        throw error;
    }
};

// Export the chat function for AI Assistant
export const callOllamaChat = callOllama;

// Helper function to extract JSON from Ollama response
const extractJSON = (text: string): any => {
    try {
        // Try to find JSON in the response
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            return JSON.parse(jsonMatch[0]);
        }

        // If no JSON found, try parsing the entire response
        return JSON.parse(text);
    } catch (error) {
        console.error('Error parsing JSON from Ollama response:', error);
        return null;
    }
};

// --- Form Generation Service ---
export const generateFormSchema = async (prompt: string): Promise<FormField[]> => {
    const systemPrompt = `You are an expert form designer. Based on the user's request, create a JSON object that defines the form fields.

The JSON should have this exact structure:
{
  "fields": [
    {
      "name": "field_name_in_snake_case",
      "label": "Human Readable Label",
      "type": "text|textarea|date|select|number|file",
      "required": true|false,
      "options": ["option1", "option2"] // only for select type
    }
  ]
}

Rules:
- Use snake_case for field names
- Type must be one of: text, textarea, date, select, number, file
- Include options array only for select type fields
- Make logical decisions about which fields should be required
- Return ONLY the JSON, no additional text`;

    try {
        const response = await callOllama(`Design a form for the following request: "${prompt}"`, systemPrompt);
        const result = extractJSON(response);

        if (result && result.fields && Array.isArray(result.fields)) {
            return result.fields;
        }

        // Fallback: return a simple default form if parsing fails
        console.warn('Failed to parse form schema, using fallback');
        return [
            { name: 'user_name', label: 'Your Name', type: 'text', required: true },
            { name: 'description', label: 'Description', type: 'textarea', required: true },
            { name: 'priority', label: 'Priority', type: 'select', required: true, options: ['Low', 'Medium', 'High'] }
        ];
    } catch (error) {
        console.error("Error generating form schema:", error);
        // Return a basic fallback form
        return [
            { name: 'user_name', label: 'Your Name', type: 'text', required: true },
            { name: 'description', label: 'Description', type: 'textarea', required: true }
        ];
    }
};


// --- Ticket Analysis Services ---
export const analyzeTicketDescription = async (description: string): Promise<Omit<AnalyzedTicketInfo, 'suggestedAssignees' | 'workPlan'>> => {
    const systemPrompt = `You are an AI expert at analyzing ticket descriptions and extracting structured information.

Analyze the ticket description and return a JSON object with this exact structure:
{
  "title": "Concise title (max 8 words)",
  "category": "Category from allowed list",
  "urgency": "Urgency level",
  "relatedDepartments": ["Department1", "Department2"]
}

Allowed categories: "IT Support", "Finance", "Admin Request", "Marketing", "Human Resources", "Quality Assurance", "Operations", "R&D"
Allowed urgency levels: "Low", "Medium", "High"
Allowed departments: "Marketing", "IT", "Sales Team A", "Sales Team B", "Assembly", "Production", "Fabrication", "Finance", "Human Resources", "Quality Assurance", "System", "R&D", "Operations"

Return ONLY the JSON, no additional text.`;

    try {
        const response = await callOllama(`Analyze the following ticket description:\n\n"${description}"`, systemPrompt);
        const result = extractJSON(response);

        if (result && result.title && result.category && result.urgency) {
            return {
                title: result.title,
                category: result.category as TicketCategory,
                urgency: result.urgency as TicketUrgency,
                relatedDepartments: result.relatedDepartments || []
            };
        }

        // Fallback analysis based on keywords
        console.warn('Failed to parse ticket analysis, using fallback logic');
        return analyzeTicketFallback(description);
    } catch (error) {
        console.error("Error analyzing ticket description:", error);
        return analyzeTicketFallback(description);
    }
};

// Fallback function for ticket analysis using simple keyword matching
const analyzeTicketFallback = (description: string): Omit<AnalyzedTicketInfo, 'suggestedAssignees' | 'workPlan'> => {
    const lowerDesc = description.toLowerCase();

    // Determine category based on keywords
    let category: TicketCategory = "Admin Request";
    let relatedDepartments: string[] = [];

    if (lowerDesc.includes('computer') || lowerDesc.includes('laptop') || lowerDesc.includes('software') || lowerDesc.includes('network') || lowerDesc.includes('password')) {
        category = "IT Support";
        relatedDepartments = ["IT"];
    } else if (lowerDesc.includes('budget') || lowerDesc.includes('expense') || lowerDesc.includes('payment') || lowerDesc.includes('invoice')) {
        category = "Finance";
        relatedDepartments = ["Finance"];
    } else if (lowerDesc.includes('marketing') || lowerDesc.includes('campaign') || lowerDesc.includes('promotion')) {
        category = "Marketing";
        relatedDepartments = ["Marketing"];
    } else if (lowerDesc.includes('hr') || lowerDesc.includes('employee') || lowerDesc.includes('vacation') || lowerDesc.includes('leave')) {
        category = "Human Resources";
        relatedDepartments = ["Human Resources"];
    } else if (lowerDesc.includes('quality') || lowerDesc.includes('testing') || lowerDesc.includes('bug') || lowerDesc.includes('defect')) {
        category = "Quality Assurance";
        relatedDepartments = ["Quality Assurance"];
    } else if (lowerDesc.includes('research') || lowerDesc.includes('development') || lowerDesc.includes('innovation')) {
        category = "R&D";
        relatedDepartments = ["R&D"];
    } else if (lowerDesc.includes('operation') || lowerDesc.includes('process') || lowerDesc.includes('workflow')) {
        category = "Operations";
        relatedDepartments = ["Operations"];
    }

    // Determine urgency based on keywords
    let urgency: TicketUrgency = "Medium";
    if (lowerDesc.includes('urgent') || lowerDesc.includes('critical') || lowerDesc.includes('emergency') || lowerDesc.includes('asap')) {
        urgency = "High";
    } else if (lowerDesc.includes('low priority') || lowerDesc.includes('when possible') || lowerDesc.includes('not urgent')) {
        urgency = "Low";
    }

    // Generate title (first few words, cleaned up)
    const words = description.split(' ').slice(0, 6);
    const title = words.join(' ').replace(/[^\w\s]/g, '').trim();

    return {
        title: title || "Support Request",
        category,
        urgency,
        relatedDepartments
    };
};

export const suggestAssignees = async (ticketInfo: { title: string; description: string; category: TicketCategory }, users: User[]): Promise<string[]> => {
    const onlineUsers = users.filter(u => u.status === 'Online');

    try {
        // Use enhanced rule-based logic with experience consideration
        const suggestedIds = suggestAssigneesWithExperience(ticketInfo, onlineUsers);

        // Optionally enhance with Ollama for more natural selection
        const systemPrompt = `You are an AI expert in IT support and project management.

Based on the ticket information and available users, suggest 1-3 user IDs from the provided list.
Return a JSON object with this structure:
{
  "assigneeIds": ["user_id_1", "user_id_2"]
}

Consider:
- Match department expertise to ticket category
- Prefer users with lower workload
- Only suggest from the provided online users
- Return ONLY the JSON, no additional text`;

        const prompt = `
Ticket Information:
- Title: "${ticketInfo.title}"
- Description: "${ticketInfo.description}"
- Category: "${ticketInfo.category}"

Available Online Users with Experience:
${JSON.stringify(onlineUsers.map(u => ({
    id: u.id,
    name: u.name,
    department: u.department,
    role: u.role,
    workload: u.workload,
    experience: u.experience || {}
})), null, 2)}

Suggest the best assignees from this list.`;

        const response = await callOllama(prompt, systemPrompt);
        const result = extractJSON(response);

        if (result && result.assigneeIds && Array.isArray(result.assigneeIds)) {
            // Validate that suggested IDs exist in online users
            const validIds = result.assigneeIds.filter(id => onlineUsers.some(u => u.id === id));
            return validIds.length > 0 ? validIds : suggestedIds;
        }

        return suggestedIds;
    } catch (error) {
        console.error("Error suggesting assignees:", error);
        return suggestAssigneesFallback(ticketInfo, onlineUsers);
    }
};

// Enhanced assignee suggestion with experience consideration
const suggestAssigneesWithExperience = (ticketInfo: { title: string; description: string; category: TicketCategory }, onlineUsers: User[]): string[] => {
    // Map categories to experience keys
    const categoryExperienceMap: Record<TicketCategory, keyof User['experience']> = {
        "IT Support": "itSupport",
        "Finance": "finance",
        "Admin Request": "adminRequest",
        "Marketing": "marketing",
        "Human Resources": "humanResources",
        "Quality Assurance": "qualityAssurance",
        "Operations": "operations",
        "R&D": "rnd"
    };

    const experienceKey = categoryExperienceMap[ticketInfo.category];

    // Score users based on experience, workload, and department match
    const scoredUsers = onlineUsers.map(user => {
        let score = 0;

        // Experience score (0-100)
        const experienceScore = user.experience?.[experienceKey] || 0;
        score += experienceScore * 0.4; // 40% weight for experience

        // Department match bonus
        const categoryDepartmentMap: Record<TicketCategory, string[]> = {
            "IT Support": ["IT", "System"],
            "Finance": ["Finance"],
            "Admin Request": ["Human Resources", "Operations"],
            "Marketing": ["Marketing"],
            "Human Resources": ["Human Resources"],
            "Quality Assurance": ["Quality Assurance"],
            "Operations": ["Operations", "Assembly", "Fabrication"],
            "R&D": ["R&D"]
        };

        const preferredDepartments = categoryDepartmentMap[ticketInfo.category] || [];
        if (preferredDepartments.includes(user.department)) {
            score += 30; // Department match bonus
        }

        // Role bonus
        if (user.role === 'Manager') score += 15;
        if (user.role === 'Engineer' || user.role === 'QA Engineer') score += 10;
        if (user.role === 'Technician') score += 5;

        // Workload penalty (prefer users with lower workload)
        const totalWorkload = user.workload.urgent * 2 + user.workload.normal;
        score -= totalWorkload * 2; // Penalty for high workload

        return { user, score, experienceScore };
    });

    // Sort by score (highest first) and return top 2-3 users
    const sortedUsers = scoredUsers.sort((a, b) => b.score - a.score);
    const topUsers = sortedUsers.slice(0, Math.min(3, sortedUsers.length));

    // Ensure we have at least one user, fallback to basic logic if needed
    if (topUsers.length === 0) {
        return suggestAssigneesFallback(ticketInfo, onlineUsers);
    }

    return topUsers.map(u => u.user.id);
};

// Fallback function for assignee suggestion using rule-based logic
const suggestAssigneesFallback = (ticketInfo: { title: string; description: string; category: TicketCategory }, onlineUsers: User[]): string[] => {
    // Map categories to preferred departments
    const categoryDepartmentMap: Record<TicketCategory, string[]> = {
        "IT Support": ["IT", "System"],
        "Finance": ["Finance"],
        "Admin Request": ["Human Resources", "Operations"],
        "Marketing": ["Marketing"],
        "Human Resources": ["Human Resources"],
        "Quality Assurance": ["Quality Assurance"],
        "Operations": ["Operations", "Production"],
        "R&D": ["R&D"]
    };

    const preferredDepartments = categoryDepartmentMap[ticketInfo.category] || [];

    // Find users in preferred departments
    let candidates = onlineUsers.filter(u => preferredDepartments.includes(u.department));

    // If no users in preferred departments, use all online users
    if (candidates.length === 0) {
        candidates = onlineUsers;
    }

    // Sort by workload (prefer lower workload)
    candidates.sort((a, b) => {
        const aTotal = a.workload.urgent + a.workload.normal;
        const bTotal = b.workload.urgent + b.workload.normal;
        return aTotal - bTotal;
    });

    // Return top 1-2 candidates
    return candidates.slice(0, 2).map(u => u.id);
};


export const generateTicketWorkPlan = async (ticketTitle: string, ticketDescription: string): Promise<TicketWorkPlan> => {
    const systemPrompt = `You are an AI assistant that creates actionable work plans for support tickets.

Generate a work plan with this exact JSON structure:
{
  "overview": "Brief one-paragraph overview of the problem",
  "resolution": "One-paragraph summary of recommended resolution steps",
  "workBreakdown": ["Step 1", "Step 2", "Step 3", "Step 4"]
}

The tone should be formal and informative. Include 3-4 actionable steps in the workBreakdown.
Return ONLY the JSON, no additional text.`;

    const prompt = `Ticket Title: "${ticketTitle}"

Ticket Description: "${ticketDescription}"

Generate a detailed work plan to guide the assignee on resolving this ticket.`;

    try {
        const response = await callOllama(prompt, systemPrompt);
        const result = extractJSON(response);

        if (result && result.overview && result.resolution && result.workBreakdown) {
            return {
                overview: result.overview,
                resolution: result.resolution,
                workBreakdown: Array.isArray(result.workBreakdown) ? result.workBreakdown : []
            };
        }

        // Fallback work plan generation
        console.warn('Failed to parse work plan, using fallback');
        return generateWorkPlanFallback(ticketTitle, ticketDescription);
    } catch (error) {
        console.error("Error generating ticket work plan:", error);
        return generateWorkPlanFallback(ticketTitle, ticketDescription);
    }
};

// Fallback function for work plan generation
const generateWorkPlanFallback = (ticketTitle: string, ticketDescription: string): TicketWorkPlan => {
    const lowerDesc = ticketDescription.toLowerCase();

    let overview = `The user has reported an issue: ${ticketTitle}. `;
    let resolution = "To resolve this issue, we will follow standard troubleshooting procedures. ";
    let workBreakdown = [
        "Acknowledge the ticket and contact the user for additional details",
        "Investigate the reported issue and identify potential causes",
        "Implement the appropriate solution or workaround",
        "Test the solution and confirm resolution with the user"
    ];

    // Customize based on ticket type
    if (lowerDesc.includes('computer') || lowerDesc.includes('laptop') || lowerDesc.includes('software')) {
        overview += "This appears to be a technical hardware or software issue that requires IT support.";
        resolution = "We will diagnose the technical problem, apply appropriate fixes, and ensure the system is functioning properly.";
        workBreakdown = [
            "Contact user to gather detailed information about the technical issue",
            "Perform remote or on-site diagnostic testing",
            "Apply necessary software updates, patches, or hardware replacements",
            "Verify system functionality and provide user training if needed"
        ];
    } else if (lowerDesc.includes('password') || lowerDesc.includes('access') || lowerDesc.includes('login')) {
        overview += "This is an access-related issue requiring account management.";
        resolution = "We will verify user identity and restore appropriate access permissions.";
        workBreakdown = [
            "Verify user identity through established security protocols",
            "Check account status and permission levels",
            "Reset credentials or restore access as appropriate",
            "Confirm successful login and document the resolution"
        ];
    } else if (lowerDesc.includes('request') || lowerDesc.includes('need')) {
        overview += "This is a service request that requires fulfillment of user needs.";
        resolution = "We will process the request according to company policies and procedures.";
        workBreakdown = [
            "Review the request details and verify compliance with policies",
            "Obtain necessary approvals if required",
            "Fulfill the request or provide alternative solutions",
            "Confirm user satisfaction and close the ticket"
        ];
    }

    return { overview, resolution, workBreakdown };
};

export const generateCompletionReport = async (ticketTitle: string, ticketDescription: string): Promise<CompletionReport> => {
    const systemPrompt = `You are an AI assistant that creates post-resolution summaries for completed support tickets.

Generate a completion report with this exact JSON structure:
{
  "summaryOfResolution": "Brief paragraph summary of what was done (past tense)",
  "identifiedRootCause": "One sentence analysis of the root cause",
  "nextStepsOrRecommendations": ["Recommendation 1", "Recommendation 2"]
}

The tone should be professional and conclusive. Include 2-3 recommendations if applicable.
Return ONLY the JSON, no additional text.`;

    const prompt = `Ticket Title: "${ticketTitle}"

Ticket Description: "${ticketDescription}"

This ticket has been resolved. Generate a professional completion report.`;

    try {
        const response = await callOllama(prompt, systemPrompt);
        const result = extractJSON(response);

        if (result && result.summaryOfResolution && result.identifiedRootCause) {
            return {
                summaryOfResolution: result.summaryOfResolution,
                identifiedRootCause: result.identifiedRootCause,
                nextStepsOrRecommendations: Array.isArray(result.nextStepsOrRecommendations) ? result.nextStepsOrRecommendations : []
            };
        }

        // Fallback completion report
        console.warn('Failed to parse completion report, using fallback');
        return generateCompletionReportFallback(ticketTitle, ticketDescription);
    } catch (error) {
        console.error("Error generating completion report:", error);
        return generateCompletionReportFallback(ticketTitle, ticketDescription);
    }
};

// Fallback function for completion report generation
const generateCompletionReportFallback = (ticketTitle: string, ticketDescription: string): CompletionReport => {
    const lowerDesc = ticketDescription.toLowerCase();

    let summaryOfResolution = `The reported issue "${ticketTitle}" has been successfully resolved. `;
    let identifiedRootCause = "The root cause was identified through systematic troubleshooting and analysis.";
    let nextStepsOrRecommendations: string[] = [
        "Monitor the system for any recurring issues",
        "Document the solution for future reference"
    ];

    // Customize based on ticket type
    if (lowerDesc.includes('computer') || lowerDesc.includes('laptop') || lowerDesc.includes('software')) {
        summaryOfResolution += "Our technical team performed diagnostic tests, applied necessary updates, and verified system functionality. The hardware/software issue has been completely resolved.";
        identifiedRootCause = "The issue was caused by outdated software components or hardware malfunction.";
        nextStepsOrRecommendations = [
            "Schedule regular system maintenance and updates",
            "Provide user training on proper system usage",
            "Monitor system performance for the next 30 days"
        ];
    } else if (lowerDesc.includes('password') || lowerDesc.includes('access') || lowerDesc.includes('login')) {
        summaryOfResolution += "User identity was verified and access credentials were successfully reset. The user can now access all required systems and applications.";
        identifiedRootCause = "The access issue was due to expired credentials or account lockout.";
        nextStepsOrRecommendations = [
            "Remind user about password policy and expiration dates",
            "Enable multi-factor authentication for enhanced security"
        ];
    } else if (lowerDesc.includes('request') || lowerDesc.includes('need')) {
        summaryOfResolution += "The service request was processed according to company policies. All required approvals were obtained and the request has been fulfilled to the user's satisfaction.";
        identifiedRootCause = "This was a standard service request requiring administrative processing.";
        nextStepsOrRecommendations = [
            "Follow up with user to ensure continued satisfaction",
            "Update service catalog if this is a common request type"
        ];
    } else {
        summaryOfResolution += "The support team investigated the issue, implemented appropriate solutions, and confirmed resolution with the user.";
    }

    return {
        summaryOfResolution,
        identifiedRootCause,
        nextStepsOrRecommendations
    };
};