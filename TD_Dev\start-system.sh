#!/bin/bash

# Set colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "========================================"
echo "🚀 Daikin AI System - One-Click Startup"
echo "========================================"
echo -e "${NC}"

# Switch to project root directory
cd "$(dirname "$0")/.."

# Check Node.js
echo -e "${BLUE}📋 Checking system environment...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Error: Node.js not found${NC}"
    echo "Please download and install Node.js from https://nodejs.org"
    exit 1
fi

echo -e "${GREEN}✅ Node.js installed${NC}"
node --version

# Check npm
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ Error: npm not found${NC}"
    exit 1
fi

echo -e "${GREEN}✅ npm installed${NC}"
npm --version

# Check Ollama service
echo ""
echo -e "${BLUE}📡 Checking Ollama service...${NC}"
if ! curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Warning: Ollama service not running${NC}"
    echo "Attempting to start Ollama..."

    # Try to start Ollama
    if command -v ollama &> /dev/null; then
        ollama serve &
        sleep 5

        # Check again
        if ! curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
            echo -e "${RED}❌ Failed to start Ollama service${NC}"
            echo "Please manually run: ollama serve"
            echo "Then restart this script"
            exit 1
        fi
    else
        echo -e "${RED}❌ Ollama command not found${NC}"
        echo "Please download and install Ollama from https://ollama.ai"
        exit 1
    fi
fi

echo -e "${GREEN}✅ Ollama service is running${NC}"

# Check llama3.1:8b model
echo ""
echo -e "${BLUE}🤖 Checking AI model...${NC}"
if ! ollama list | grep -q "llama3.1:8b"; then
    echo -e "${YELLOW}⚠️  llama3.1:8b model not found${NC}"
    echo "Downloading model (this may take several minutes)..."
    ollama pull llama3.1:8b
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Model download failed${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ AI model ready${NC}"

# Install dependencies
echo ""
echo -e "${BLUE}📦 Installing project dependencies...${NC}"
if [ ! -d "node_modules" ]; then
    echo "Installing dependency packages..."
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Dependency installation failed${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Dependencies already installed${NC}"
fi

# Start development server
echo ""
echo -e "${GREEN}🌟 Starting Daikin AI System...${NC}"
echo ""
echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}🎯 System startup successful!${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""
echo -e "${BLUE}📱 Access URL: http://localhost:5173${NC}"
echo -e "${BLUE}🔧 Admin Panel: http://localhost:5173/dashboard${NC}"
echo -e "${BLUE}🤖 AI Assistant: http://localhost:5173 (click AI Assistant tab)${NC}"
echo ""
echo -e "${YELLOW}💡 Test Features:${NC}"
echo "  • Smart Forms"
echo "  • Maintenance Dashboard"
echo "  • Predictive Analytics"
echo "  • AI Assistant Chat"
echo ""
echo -e "${RED}🛑 Press Ctrl+C to stop server${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# Wait 3 seconds then open browser
sleep 3

# Try to open browser
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:5173 &
elif command -v open &> /dev/null; then
    open http://localhost:5173 &
fi

# Start development server
npm run dev
