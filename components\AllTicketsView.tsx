




import React, { useState, useMemo, useContext } from 'react';
import type { Ticket, TicketStatus, TicketCategory, TicketUrgency } from '../types';
import { AppContext } from './AppContext';
import { TICKET_STATUSES, TICKET_CATEGORIES } from '../constants';
import { ChevronDownIcon, SearchIcon } from './icons/Icons';

// --- Reusable Components ---
const StatusBadge: React.FC<{ status: TicketStatus }> = ({ status }) => {
  const statusStyles: Record<TicketStatus, string> = {
    Received: 'bg-blue-100 text-blue-800',
    'In Progress': 'bg-violet-100 text-violet-800',
    'Waiting Feedback': 'bg-orange-100 text-orange-800',
    Done: 'bg-green-100 text-green-800',
    Reopened: 'bg-pink-100 text-pink-800',
    Canceled: 'bg-gray-200 text-gray-700 line-through',
    Closed: 'bg-gray-200 text-gray-700',
  };
  return <span className={`px-2.5 py-1 text-xs font-semibold rounded-full ${statusStyles[status]}`}>{status}</span>;
};

const UrgencyDisplay: React.FC<{ urgency: TicketUrgency }> = ({ urgency }) => {
    const urgencyColors: Record<TicketUrgency, string> = {
        High: "text-red-600",
        Medium: "text-orange-600",
        Low: "text-green-600"
    };
    return <span className={`${urgencyColors[urgency]}`}>{urgency}</span>
}

const AssigneeAvatars: React.FC<{ userIds: string[] }> = ({ userIds }) => {
  const { users } = useContext(AppContext);
  return (
    <div className="flex items-center -space-x-3">
      {userIds.slice(0, 3).map(id => {
        const user = users.find(u => u.id === id);
        return user ? (
          <div key={id} title={user.name} className="h-8 w-8 rounded-full bg-gray-300 border-2 border-white flex items-center justify-center text-sm font-bold text-gray-700">
            {user.avatar}
          </div>
        ) : null;
      })}
      {userIds.length > 3 && (
        <div className="h-8 w-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs font-bold text-gray-500">
          +{userIds.length - 3}
        </div>
      )}
    </div>
  );
};


// --- Main View Component ---
const AllTicketsView: React.FC<{ onSelectTicket: (id: string) => void }> = ({ onSelectTicket }) => {
  const { tickets } = useContext(AppContext);
  
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<{status: TicketStatus | 'All', category: TicketCategory | 'All'}>({
      status: 'All',
      category: 'All'
  });
  
  const [sortConfig, setSortConfig] = useState<{key: keyof Ticket | null; direction: 'asc' | 'desc'}>({
      key: 'createdAt',
      direction: 'desc'
  });
  
  const handleFilterChange = (type: 'status' | 'category', value: string) => {
      setFilters(prev => ({...prev, [type]: value}));
  }
  
  const handleSort = (key: keyof Ticket) => {
      let direction: 'asc' | 'desc' = 'asc';
      if (sortConfig.key === key && sortConfig.direction === 'asc') {
          direction = 'desc';
      }
      setSortConfig({ key, direction });
  }

  const filteredAndSortedTickets = useMemo(() => {
      let filtered = [...tickets];
      
      if (searchQuery) {
          const lowercasedQuery = searchQuery.toLowerCase();
          filtered = filtered.filter(t => 
              t.id.toLowerCase().includes(lowercasedQuery) ||
              t.title.toLowerCase().includes(lowercasedQuery) ||
              t.description.toLowerCase().includes(lowercasedQuery)
          );
      }

      if (filters.status !== 'All') {
          filtered = filtered.filter(t => t.status === filters.status);
      }
      if (filters.category !== 'All') {
          filtered = filtered.filter(t => t.category === filters.category);
      }
      
      if (sortConfig.key) {
        filtered.sort((a, b) => {
          const aValue = a[sortConfig.key as keyof Ticket];
          const bValue = b[sortConfig.key as keyof Ticket];
          if (aValue && bValue) {
            if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
            if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
          }
          return 0;
        });
      }

      return filtered;
  }, [tickets, filters, sortConfig, searchQuery]);

  const SortableHeader: React.FC<{label: string; sortKey: keyof Ticket}> = ({label, sortKey}) => (
      <th scope="col" className="px-6 py-3 cursor-pointer whitespace-nowrap" onClick={() => handleSort(sortKey)}>
          <div className="flex items-center gap-1">
              {label}
              {sortConfig.key === sortKey && <span className={`transition-transform ${sortConfig.direction === 'asc' ? 'rotate-180' : ''}`}><ChevronDownIcon className="w-3 h-3"/></span>}
          </div>
      </th>
  );
  
  return (
    <div className="bg-gray-50 flex-1 flex flex-col overflow-hidden">
        {/* Toolbar */}
        <div className="p-4 bg-white flex items-center justify-between gap-4 border-b border-gray-200">
            <div className="flex items-center gap-4">
                <FilterDropdown 
                    label="Status"
                    options={['All', ...TICKET_STATUSES]}
                    value={filters.status}
                    onChange={(val) => handleFilterChange('status', val)}
                />
                <FilterDropdown 
                    label="Category"
                    options={['All', ...TICKET_CATEGORIES]}
                    value={filters.category}
                    onChange={(val) => handleFilterChange('category', val)}
                />
            </div>
             <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                   <SearchIcon className="h-4 w-4 text-gray-400" />
                </div>
                <input
                    type="text"
                    placeholder="Search tickets..."
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="block w-64 rounded-md border-0 bg-white py-1.5 pl-9 pr-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                />
            </div>
        </div>
        <div className="flex-1 overflow-auto">
            <table className="w-full text-sm text-left text-gray-500">
                <thead className="text-xs text-gray-700 uppercase bg-gray-100 sticky top-0 z-10">
                    <tr>
                        <SortableHeader label="Ticket ID" sortKey="id" />
                        <SortableHeader label="Title" sortKey="title" />
                        <SortableHeader label="Status" sortKey="status" />
                        <SortableHeader label="Category" sortKey="category" />
                        <SortableHeader label="Urgency" sortKey="urgency" />
                        <th scope="col" className="px-6 py-3 whitespace-nowrap">Assignees</th>
                        <SortableHeader label="Created" sortKey="createdAt" />
                    </tr>
                </thead>
                <tbody className="bg-white">
                    {filteredAndSortedTickets.map(ticket => (
                        <tr key={ticket.id} onClick={() => onSelectTicket(ticket.id)} className="border-b border-gray-200 hover:bg-gray-50 cursor-pointer">
                            <td className="px-6 py-4 font-medium text-gray-700 whitespace-nowrap">{ticket.id}</td>
                            <td className="px-6 py-4 font-semibold text-gray-900">{ticket.title}</td>
                            <td className="px-6 py-4 whitespace-nowrap"><StatusBadge status={ticket.status} /></td>
                            <td className="px-6 py-4 whitespace-nowrap">{ticket.category}</td>
                            <td className="px-6 py-4 whitespace-nowrap"><UrgencyDisplay urgency={ticket.urgency} /></td>
                            <td className="px-6 py-4">
                                {ticket.assignees.length > 0 ? <AssigneeAvatars userIds={ticket.assignees} /> : <span className="text-gray-500">Unassigned</span>}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">{new Date(ticket.createdAt).toLocaleDateString()}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    </div>
  );
};

const FilterDropdown: React.FC<{
    label: string,
    options: string[],
    value: string,
    onChange: (value: string) => void
}> = ({ label, options, value, onChange }) => (
    <div className="flex items-center gap-2">
        <label className="text-sm text-gray-600">{label}:</label>
        <select 
            value={value} 
            onChange={e => onChange(e.target.value)}
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md focus:ring-blue-500 focus:border-blue-500 block p-1.5"
        >
            {options.map(opt => <option key={opt} value={opt}>{opt}</option>)}
        </select>
    </div>
);

export default AllTicketsView;