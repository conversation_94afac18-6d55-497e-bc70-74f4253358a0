

import React, { useState, useMemo } from 'react';
import type { User, AppView } from './types';
import { USERS } from './constants';
import UserSelector from './components/UserSelector';
import { AppProvider } from './components/AppContext';
import { DaikinLogo, BotIcon, WrenchScrewdriverIcon, BookOpenIcon, ClipboardDocumentListIcon } from './components/icons/Icons';
import ChatInterface from './components/ChatInterface';
import MaintenanceDashboard from './components/MaintenanceDashboard';
import Wiki from './components/Wiki';
import SmartFormPage from './components/SmartFormPage';



const App: React.FC = () => {
  const [loggedInUser] = useState<User>(USERS[0]); // This is always Demo Admin, the "real" user.
  const [impersonatedUser, setImpersonatedUser] = useState<User>(USERS[0]); // This is who we are "acting as".
  const [activeView, setActiveView] = useState<AppView>('maintenance');

  const handleUserChange = (user: User) => {
    setImpersonatedUser(user);
    // When user changes, we might want to reset the view to a default state
    setActiveView('maintenance'); 
  };
  
  const MainContent = useMemo(() => {
    switch (activeView) {
      case 'ai-assistant':
        return <ChatInterface />;
      case 'maintenance':
        return <MaintenanceDashboard />;
      case 'wiki':
        return <Wiki />;
      case 'smart-form':
      default:
        // SmartFormPage and its children will get data from AppContext
        return <SmartFormPage />;
    }
  }, [activeView]);

  return (
    <AppProvider selectedUser={impersonatedUser} setActiveView={setActiveView}>
      <div className="flex min-h-screen bg-gray-100 text-gray-800 font-sans antialiased">
        {/* Sidebar */}
        <aside className="w-60 bg-white border-r border-gray-200 flex flex-col">
          <div className="flex items-center gap-3 h-16 px-4 border-b border-gray-200">
            <DaikinLogo className="h-8 w-auto" />
          </div>
          <nav className="flex-1 p-4 space-y-1">

            <NavItem icon={<WrenchScrewdriverIcon />} label="Maintenance" active={activeView === 'maintenance'} onClick={() => setActiveView('maintenance')} />
            <NavItem icon={<ClipboardDocumentListIcon />} label="Smart Form" active={activeView === 'smart-form'} onClick={() => setActiveView('smart-form')} />
            <NavItem icon={<BookOpenIcon />} label="Wiki" active={activeView === 'wiki'} onClick={() => setActiveView('wiki')} />
            <NavItem icon={<BotIcon />} label="AI Assistant" active={activeView === 'ai-assistant'} onClick={() => setActiveView('ai-assistant')} />
          </nav>
          <div className="p-4 border-t border-gray-200">
            <p className="text-xs text-gray-500 text-center">&copy; 2025 Daikin Systems</p>
          </div>
        </aside>

        {/* Main Area */}
        <div className="flex-1 flex flex-col relative">
          {/* Top Header */}
          <header className="flex items-center justify-between h-16 px-6 bg-white border-b border-gray-200 z-10">
            <div className="flex items-center gap-4">
              {/* Search can be re-enabled later */}
            </div>
            <div className="flex items-center gap-4">
              <UserSelector
                users={USERS}
                loggedInUser={loggedInUser}
                selectedUser={impersonatedUser}
                onSelectUser={handleUserChange}
              />
            </div>
          </header>

          {/* Main Content */}
          <main className="flex-1 overflow-y-auto">
            {MainContent}
          </main>
          
        </div>
      </div>
    </AppProvider>
  );
};

interface NavItemProps {
  icon: React.ReactElement<React.SVGProps<SVGSVGElement>>;
  label: string;
  active: boolean;
  onClick: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ icon, label, active, onClick }) => (
  <button onClick={onClick} className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors ${active ? 'bg-blue-50 text-blue-600' : 'text-gray-500 hover:bg-gray-100 hover:text-gray-900'}`}>
    <span className={active ? 'text-blue-600' : 'text-gray-400'}>{React.cloneElement(icon, { className: 'h-5 w-5' })}</span>
    {label}
  </button>
);


export default App;