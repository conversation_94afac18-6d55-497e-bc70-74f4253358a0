# AIT Systems Demo Data Preparation Guide

## 🎯 Demo Data Overview

This document details the test data, user account setup, and scenario configuration required for customer demonstrations, ensuring smooth and convincing demo processes.

---

## 👥 Demo User Account Setup

### Primary Demo Accounts
1. **Demo Admin (Super User)**
   - Username: <EMAIL>
   - Role: Super User
   - Permissions: Complete system access
   - Purpose: Primary demo account, showcase all features

2. **<PERSON> (IT Manager)**
   - Username: <EMAIL>
   - Role: Manager
   - Department: IT
   - Expertise: IT Support(96%), Network Management(89%), System Maintenance(92%)
   - Purpose: Demonstrate technical management scenarios

3. **<PERSON> (Operations Manager)**
   - Username: <EMAIL>
   - Role: Manager
   - Department: Production
   - Expertise: Equipment Maintenance(94%), Production Management(91%), Quality Control(88%)
   - Purpose: Demonstrate operational management scenarios

4. **<PERSON> (IT Support)**
   - Username: <EMAIL>
   - Role: Technician
   - Department: IT
   - Expertise: Technical Support(87%), Hardware Repair(85%)
   - Purpose: Demonstrate frontline technician perspective

5. **<PERSON> (CEO)**
   - Username: <EMAIL>
   - Role: CEO
   - Permissions: Executive management permissions
   - Purpose: Demonstrate executive perspective and permission differences

---

## 🎫 Ticket Demo Data

### Preset Ticket Scenarios

#### Scenario 1: IT Support Ticket (For Demo)
```
Title: Server Performance Optimization Request
Category: IT Support
Priority: High
Description: Production environment server responding slowly, affecting production efficiency, need performance analysis and optimization
Department: Production
Status: New
Expected Assignee: David Lee (96% IT support success rate)
```

#### Scenario 2: Equipment Maintenance Ticket
```
Title: HVAC System Quarterly Maintenance
Category: Maintenance
Priority: Medium
Description: Air conditioning system needs quarterly maintenance including filter cleaning, refrigerant inspection, etc.
Department: Facilities
Status: In Progress
Assignee: Bob Smith
```

#### Scenario 3: Finance Reimbursement Ticket
```
Title: Business Travel Expense Reimbursement
Category: Finance
Priority: Low
Description: Customer visit travel expense reimbursement including transportation, accommodation, and meal expenses
Department: Sales
Status: Completed
```

### Ticket Statistics Data
- **Total Tickets**: 156
- **New This Month**: 23
- **In Progress**: 8
- **Completed**: 145
- **Average Processing Time**: 2.3 days
- **Customer Satisfaction**: 94%

---

## 🤖 AI Assistant Test Question Bank

### Basic Function Test Questions

#### High Confidence Questions (90%+)
1. **"How do I reset my password?"**
   - Expected Confidence: 95%
   - Answer Source: IT Security Policy
   - Demo Points: Standard process, security requirements

2. **"What are the working hours?"**
   - Expected Confidence: 97%
   - Answer Source: HR Policy
   - Demo Points: Flexible work hours, overtime policy

3. **"How do I submit an expense report?"**
   - Expected Confidence: 88%
   - Answer Source: Finance Policy
   - Demo Points: Process steps, required materials

#### Wiki Knowledge Test Questions

4. **"What is our company philosophy?"**
   - Expected Confidence: 95%
   - Answer Source: Wiki Knowledge Article
   - Demo Points: People-centered management philosophy, corporate culture

5. **"What is inverter technology?"**
   - Expected Confidence: 93%
   - Answer Source: Wiki Technical Article
   - Demo Points: Technical principles, application advantages

6. **"What is the Fusion 25 plan?"**
   - Expected Confidence: 91%
   - Answer Source: Wiki Strategic Document
   - Demo Points: Strategic goals, implementation roadmap

#### Technical Professional Questions

7. **"How do I interpret fault codes on the maintenance dashboard?"**
   - Expected Confidence: 88%
   - Answer Source: Maintenance Guide
   - Demo Points: Code format, priority classification

8. **"What are the safety protocols for refrigerants?"**
   - Expected Confidence: 96%
   - Answer Source: Safety Manual
   - Demo Points: Safety requirements, operational standards

### Permission Test Scenarios

#### Different Roles Same Question Test
**Question**: "What is our financial performance this quarter?"

- **CEO Role**: Shows detailed financial data and analysis
- **Manager Role**: Shows department-related financial information
- **Worker Role**: Shows "Insufficient permissions" prompt

#### Sensitive Information Access Test
**Question**: "What is the salary structure?"

- **HR Manager**: Shows complete compensation system
- **Regular Employee**: Shows permission restriction information

---

## 📊 Equipment Monitoring Demo Data

### Equipment Status Setup

#### Normal Operating Equipment (Green)
1. **HVAC-002**: Operating normally, 98% efficiency
2. **PROD-001**: Production equipment, stable operation
3. **NET-001**: Network equipment, normal connection

#### Equipment Requiring Attention (Yellow)
1. **HVAC-001**: Efficiency dropped to 85%, maintenance recommended
2. **PROD-003**: Abnormal vibration, inspection needed
3. **POWER-002**: High load, monitoring required

#### Faulty Equipment (Red)
1. **HVAC-003**: Refrigerant leak, emergency repair needed
2. **PROD-002**: Sensor failure, shut down
3. **NET-003**: Network interruption, affecting communication

### Predictive Analysis Data

#### HVAC-001 Predictive Analysis Results
```
Equipment ID: HVAC-001
Current Status: Requires Attention
Failure Probability: 85% (next 7 days)
Estimated Maintenance Cost: $2,250
Recommended Maintenance Time: Within 3 days
Maintenance Type: Preventive maintenance
Expected Effect: Extend equipment life by 6 months
```

#### System-Level Analysis Results
```
Total Equipment: 24 units
Healthy Equipment: 18 units (75%)
Requires Attention: 4 units (17%)
Faulty Equipment: 2 units (8%)
Estimated Monthly Maintenance Cost: $6,750
Savings through Predictive Maintenance: $2,025 (30%)
```

---

## 📈 Dashboard Data Configuration

### Main Dashboard KPI Metrics
1. **Ticket Processing Efficiency**: 94% (Target: 90%)
2. **Equipment Uptime Rate**: 92% (Target: 95%)
3. **Customer Satisfaction**: 4.7/5.0
4. **Average Response Time**: 2.1 hours (Target: 4 hours)
5. **Cost Savings**: $18,750/month

### Trend Chart Data
- **Ticket Volume Trend**: Steady growth over past 6 months
- **Processing Time Trend**: Continuous optimization, 30% average time reduction
- **Equipment Health**: Overall stable, individual equipment needs attention
- **User Activity**: 85% daily active users, 98% weekly active users

---

## 🏢 Organization Structure Demo Data

### Department Structure
```
AIT Systems
├── Executive (CEO: Alice Johnson)
├── Production (Manager: Bob Smith)
│   ├── Production Workers (5 people)
│   └── QA Engineers (3 people)
├── IT (Manager: David Lee)
│   ├── IT Support (Eve Williams and 3 others)
│   └── System Admins (2 people)
├── Sales (Manager: TBD)
│   └── Sales Representatives (4 people)
└── R&D (Manager: Rachel Silver)
    ├── Engineers (6 people)
    └── Technicians (4 people)
```

### Employee Skill Data Examples

#### David Lee (IT Manager)
```
Skill Expertise:
- IT Support: 96% (handled 156 tickets, 96% success rate)
- Network Management: 89% (handled 45 network issues)
- System Maintenance: 92% (responsible for 12 systems)
- Project Management: 87% (completed 8 IT projects)

Workload: Medium (currently handling 3 tickets)
Availability: Immediately available
Location: Headquarters
```

#### Bob Smith (Operations Manager)
```
Skill Expertise:
- Equipment Maintenance: 94% (maintains 23 equipment units)
- Production Management: 91% (manages production line efficiency)
- Quality Control: 88% (quality inspection pass rate)
- Team Management: 93% (manages 15-person team)

Workload: High (currently handling 5 tickets)
Availability: Available in 2 hours
Location: Factory A
```

---

## 🎯 Demo Scenario Scripts

### Scenario 1: Emergency IT Support
**Background**: Production environment server performance issues
**Demo Flow**:
1. Create high-priority IT support ticket
2. System intelligently recommends David Lee (96% success rate)
3. David Lee receives and begins processing
4. Show real-time status updates and collaboration

### Scenario 2: Predictive Maintenance
**Background**: HVAC-001 equipment efficiency decline
**Demo Flow**:
1. View equipment monitoring dashboard
2. Discover HVAC-001 status anomaly
3. Run predictive analysis
4. Show maintenance recommendations and cost savings

### Scenario 3: Knowledge Query
**Background**: New employee needs to understand company policies
**Demo Flow**:
1. Use AI assistant to query work hours policy
2. Show high-confidence answer and sources
3. Switch roles to show permission differences
4. Query technical questions to show professional capabilities

---

## ✅ Pre-Demo Checklist

### Data Verification
- [ ] All user accounts can log in normally
- [ ] Ticket data complete and status correct
- [ ] Equipment monitoring data updates in real-time
- [ ] AI assistant answers accurate with reasonable confidence
- [ ] Organization structure data complete

### Function Verification
- [ ] Smart Form creation and submission normal
- [ ] Ticket assignment algorithm working correctly
- [ ] AI assistant response speed normal
- [ ] Predictive analysis functions normal
- [ ] Permission controls effective

### Interface Verification
- [ ] All pages load normally
- [ ] Charts and visualizations display correctly
- [ ] Responsive design works on different screen sizes
- [ ] No obvious UI errors or anomalies

### Performance Verification
- [ ] Page load speed normal (<3 seconds)
- [ ] AI assistant response time reasonable (<10 seconds)
- [ ] Data queries and updates timely
- [ ] Overall system running stable

---

*Data Preparation Instructions: Recommend completing all data preparation and system checks 24 hours before the demo to ensure no data or functional anomalies occur during the demonstration. Contact the technical team immediately if issues arise.*
