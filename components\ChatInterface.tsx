
import React, { useState, useRef, useEffect, useContext } from 'react';
import type { Message, GroundedResponse, EnhancedRAGResponse, RAGSource } from '../types';
import { AppContext } from './AppContext';
import { generateAIResponse, findBestKnowledgeMatch, testOllamaConnection, generateEnhancedRAGResponse } from '../services/aiAssistantService';
import { SendIcon, BotIcon, UserIcon, BookOpenIcon, ClockIcon, CheckCircleIcon, ExclamationTriangleIcon, EyeIcon } from './icons/Icons';
import DocumentViewer from './DocumentViewer';
import RAGProcessVisualization from './RAGProcessVisualization';

const GroundedResponseDisplay: React.FC<{ response: GroundedResponse }> = ({ response }) => (
    <div className="space-y-4">
        <p className="whitespace-pre-wrap">{response.summary}</p>
        <div className="border-t border-gray-200 pt-3 text-xs">
            <p className="font-semibold text-gray-700 mb-1">Source:</p>
            <p className="flex items-center gap-2 text-gray-600"><BookOpenIcon className="w-4 h-4 text-blue-600" /> {response.source}</p>
        </div>
         <div className="border-t border-gray-200 pt-3 text-xs">
            <p className="font-semibold text-gray-700 mb-1">Quote:</p>
            <blockquote className="border-l-2 border-blue-500 pl-2 italic text-gray-500">"{response.quote}"</blockquote>
        </div>
    </div>
);

// Enhanced RAG Response Display Component
const EnhancedRAGResponseDisplay: React.FC<{
    response: EnhancedRAGResponse;
    onSourceClick?: (source: RAGSource, query: string) => void;
    originalQuery?: string;
}> = ({ response, onSourceClick, originalQuery }) => (
    <div className="space-y-4">
        <p className="whitespace-pre-wrap">{response.answer}</p>

        {/* Confidence Score */}
        <div className="flex items-center gap-2 text-sm">
            <div className="flex items-center gap-1">
                <CheckCircleIcon className="w-4 h-4 text-green-600" />
                <span className="font-medium">Confidence:</span>
                <span className={`font-bold ${response.confidence_score > 0.8 ? 'text-green-600' : response.confidence_score > 0.6 ? 'text-yellow-600' : 'text-red-600'}`}>
                    {(response.confidence_score * 100).toFixed(0)}%
                </span>
            </div>
            {response.query_processing_time && (
                <div className="flex items-center gap-1 text-gray-500">
                    <ClockIcon className="w-4 h-4" />
                    <span>{response.query_processing_time}ms</span>
                </div>
            )}
        </div>

        {/* Sources */}
        <div className="border-t border-gray-200 pt-3">
            <div className="flex items-center justify-between mb-2">
                <p className="font-semibold text-gray-700 text-sm">Sources ({response.sources.length}):</p>
                {response.sources[0]?.restricted_count && response.sources[0].restricted_count > 0 && (
                    <div className="flex items-center gap-1 text-xs text-amber-600">
                        <ExclamationTriangleIcon className="w-4 h-4" />
                        <span>{response.sources[0].restricted_count} sources restricted</span>
                    </div>
                )}
            </div>
            <div className="space-y-2">
                {response.sources.map((source, index) => (
                    <div key={source.document_id} className="bg-gray-50 rounded-lg p-3 text-xs hover:bg-gray-100 transition-colors">
                        <div className="flex items-center justify-between mb-1">
                            <div className="flex items-center gap-2">
                                <BookOpenIcon className="w-4 h-4 text-blue-600" />
                                <span className="font-medium">{source.document_id}</span>
                                <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                                    {source.document_type}
                                </span>
                                {source.page_reference && (
                                    <span className="text-gray-500">({source.page_reference})</span>
                                )}
                            </div>
                            <div className="flex items-center gap-2 flex-wrap">
                                <span className="text-gray-500">Relevance: {(source.relevance_score * 100).toFixed(0)}%</span>
                                {source.success_rate && (
                                    <span className="text-green-600">Success: {source.success_rate}</span>
                                )}
                                {source.user_experience_score && source.user_experience_score > 0 && (
                                    <span className={`px-2 py-1 rounded-full text-xs ${
                                        source.user_experience_score >= 90 ? 'bg-green-100 text-green-700' :
                                        source.user_experience_score >= 80 ? 'bg-blue-100 text-blue-700' :
                                        'bg-yellow-100 text-yellow-700'
                                    }`}>
                                        Your Experience: {source.user_experience_score}%
                                    </span>
                                )}
                                {source.access_level && (
                                    <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">
                                        Access: {source.access_level}
                                    </span>
                                )}
                                {onSourceClick && (
                                    <button
                                        onClick={() => onSourceClick(source, originalQuery || '')}
                                        className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
                                        title="View full document"
                                    >
                                        <EyeIcon className="w-4 h-4" />
                                    </button>
                                )}
                            </div>
                        </div>
                        {source.expert_author && (
                            <p className="text-gray-600 mb-1">Expert: {source.expert_author}</p>
                        )}
                        <blockquote className="border-l-2 border-blue-500 pl-2 italic text-gray-600">
                            "{source.document_excerpt}"
                        </blockquote>
                        <div className="flex items-center justify-between mt-1">
                            <p className="text-gray-500">Last updated: {new Date(source.last_updated).toLocaleDateString()}</p>
                            {source.credibility_score && (
                                <span className={`text-xs font-medium ${
                                    source.credibility_score >= 0.9 ? 'text-green-600' :
                                    source.credibility_score >= 0.7 ? 'text-yellow-600' : 'text-red-600'
                                }`}>
                                    Credibility: {(source.credibility_score * 100).toFixed(0)}%
                                </span>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>

        {/* Alternative Solutions */}
        {response.alternative_solutions && response.alternative_solutions > 0 && (
            <div className="border-t border-gray-200 pt-3">
                <p className="text-sm text-gray-600">
                    <ExclamationTriangleIcon className="w-4 h-4 inline mr-1" />
                    {response.alternative_solutions} alternative solution{response.alternative_solutions > 1 ? 's' : ''} available
                </p>
            </div>
        )}

        {/* Expert Recommendations */}
        {response.expert_recommendations && response.expert_recommendations.length > 0 && (
            <div className="border-t border-gray-200 pt-3">
                <p className="font-semibold text-gray-700 mb-2 text-sm">Expert Recommendations:</p>
                <div className="space-y-2">
                    {response.expert_recommendations.map((expert, index) => (
                        <div key={expert.expert_id} className="bg-blue-50 rounded-lg p-3 text-sm">
                            <p className="font-medium text-blue-800">{expert.expert_name}</p>
                            <p className="text-gray-700">{expert.recommendation}</p>
                            {expert.contact_info && (
                                <p className="text-blue-600 text-xs mt-1">Contact: {expert.contact_info}</p>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        )}
    </div>
);

const ChatInterface: React.FC = () => {
  const { currentUser, aiKnowledgeBase } = useContext(AppContext);
  const [messages, setMessages] = useState<Message[]>([
      { id: '1', sender: 'ai', content: `Hello ${currentUser.name}. I am the AIT Systems Knowledge Assistant powered by local AI. How can I help you today? You can ask me about company policies, product information, and more.` }
  ]);
  const [input, setInput] = useState('');
  const [isThinking, setIsThinking] = useState(false);
  const [ollamaConnected, setOllamaConnected] = useState<boolean | null>(null);
  const [selectedSource, setSelectedSource] = useState<RAGSource | null>(null);
  const [highlightText, setHighlightText] = useState<string>('');
  const [showRAGProcess, setShowRAGProcess] = useState(false);
  const [currentQuery, setCurrentQuery] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(scrollToBottom, [messages]);

  // Test Ollama connection on component mount
  useEffect(() => {
    const checkOllamaConnection = async () => {
      const connected = await testOllamaConnection();
      setOllamaConnected(connected);
      if (!connected) {
        console.warn('Ollama is not connected. Falling back to basic responses.');
      }
    };

    checkOllamaConnection();
  }, []);

  const handleSend = async () => {
      const trimmedInput = input.trim();
      if (!trimmedInput || isThinking) return;

      const userMessage: Message = { id: Date.now().toString(), sender: 'user', content: trimmedInput };
      setMessages(prev => [...prev, userMessage]);
      setCurrentQuery(trimmedInput);
      setInput('');
      setIsThinking(true);
      setShowRAGProcess(true);

      try {
          // Try enhanced RAG first
          try {
              const enhancedResponse = await generateEnhancedRAGResponse(trimmedInput, aiKnowledgeBase, currentUser);

              const aiMessage: Message = {
                  id: (Date.now() + 1).toString(),
                  sender: 'ai',
                  content: enhancedResponse
              };
              setMessages(prev => [...prev, aiMessage]);
          } catch (enhancedError) {
              console.warn('Enhanced RAG failed, falling back to basic RAG:', enhancedError);

              // Fallback to basic RAG
              const matchedItem = findBestKnowledgeMatch(trimmedInput, aiKnowledgeBase);
              const aiResponseText = await generateAIResponse(trimmedInput, matchedItem, currentUser);

              let aiResponseContent: Message['content'];

              // Try to parse as JSON for structured response, otherwise use as plain text
              try {
                  const parsed = JSON.parse(aiResponseText);
                  if (parsed.summary && parsed.source && parsed.quote) {
                      aiResponseContent = {
                          summary: parsed.summary,
                          source: parsed.source,
                          quote: parsed.quote,
                      };
                  } else {
                      aiResponseContent = aiResponseText;
                  }
              } catch {
                  aiResponseContent = aiResponseText;
              }

              const aiMessage: Message = { id: (Date.now() + 1).toString(), sender: 'ai', content: aiResponseContent };
              setMessages(prev => [...prev, aiMessage]);
          }
      } catch (error) {
          console.error('Error generating AI response:', error);
          const errorMessage: Message = {
              id: (Date.now() + 1).toString(),
              sender: 'ai',
              content: "I'm sorry, I'm experiencing technical difficulties. Please try again later or contact your IT support."
          };
          setMessages(prev => [...prev, errorMessage]);
      } finally {
          setIsThinking(false);
          // Hide RAG process visualization after a brief delay
          setTimeout(() => setShowRAGProcess(false), 1000);
      }
  };

  const handleSourceClick = (source: RAGSource, query: string) => {
      setSelectedSource(source);
      setHighlightText(query);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          handleSend();
      }
  };

  return (
    <div className="flex flex-col h-full bg-gray-50 text-gray-800">

      <header className="px-6 py-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">AI Knowledge Assistant</h2>
            <p className="text-sm text-gray-500">Your trusted source for company information, grounded in internal documents.</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${ollamaConnected === true ? 'bg-green-500' : ollamaConnected === false ? 'bg-red-500' : 'bg-yellow-500'}`}></div>
              <span className="text-xs text-gray-500">
                {ollamaConnected === true ? 'Ollama Connected' : ollamaConnected === false ? 'Ollama Offline' : 'Checking...'}
              </span>
            </div>
          </div>
        </div>
      </header>
      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {messages.map((msg) => (
          <div key={msg.id} className={`flex items-start gap-4 max-w-4xl mx-auto ${msg.sender === 'user' ? 'justify-end' : ''}`}>
             {msg.sender === 'ai' && (
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                    <BotIcon className="w-5 h-5 text-white"/>
                </div>
            )}
            <div className={`p-4 rounded-xl prose prose-sm prose-p:my-0 prose-p:leading-relaxed ${msg.sender === 'user' ? 'bg-blue-600 text-white rounded-br-none' : 'bg-gray-200 text-gray-800 rounded-bl-none'}`}>
              {typeof msg.content === 'string' ? (
                <p className="whitespace-pre-wrap">{msg.content}</p>
              ) : msg.content && typeof msg.content === 'object' && 'answer' in msg.content ? (
                <EnhancedRAGResponseDisplay
                  response={msg.content as EnhancedRAGResponse}
                  onSourceClick={handleSourceClick}
                  originalQuery={messages.find(m => m.sender === 'user' && messages.indexOf(m) < messages.indexOf(msg))?.content as string}
                />
              ) : (
                <GroundedResponseDisplay response={msg.content as GroundedResponse} />
              )}
            </div>
             {msg.sender === 'user' && (
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                    <UserIcon className="w-5 h-5 text-gray-600"/>
                </div>
            )}
          </div>
        ))}

        {/* RAG Process Visualization */}
        {showRAGProcess && (
          <div className="max-w-4xl mx-auto">
            <RAGProcessVisualization
              isProcessing={isThinking}
              query={currentQuery}
              onComplete={() => {
                // Optional: Add any completion logic here
              }}
            />
          </div>
        )}

        {isThinking && (
            <div className="flex items-start gap-4 max-w-4xl mx-auto">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                    <BotIcon className="w-5 h-5 text-white"/>
                </div>
                <div className="p-4 rounded-xl bg-gray-200 text-gray-800 rounded-bl-none">
                    <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse delay-0"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse delay-150"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse delay-300"></div>
                    </div>
                </div>
            </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      <div className="p-6 border-t border-gray-200 bg-white">
        <div className="flex items-center bg-gray-100 border border-gray-300 rounded-lg px-4 py-2 max-w-4xl mx-auto">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSend()}
            placeholder="Ask a question about company knowledge..."
            className="flex-1 bg-transparent text-gray-800 placeholder-gray-500 focus:outline-none"
            disabled={isThinking}
          />
          <button onClick={handleSend} disabled={!input.trim() || isThinking} className="p-2 rounded-md text-gray-500 hover:text-blue-600 hover:bg-gray-200 disabled:text-gray-300 disabled:bg-transparent disabled:cursor-not-allowed transition-colors">
            <SendIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Document Viewer Modal */}
      {selectedSource && (
        <DocumentViewer
          source={selectedSource}
          onClose={() => setSelectedSource(null)}
          highlightText={highlightText}
        />
      )}
    </div>
  );
};

export default ChatInterface;