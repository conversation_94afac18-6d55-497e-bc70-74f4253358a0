# 🧪 Daikin AI System - Complete Testing Guide

## 📋 Table of Contents
1. [Quick Start](#quick-start)
2. [System Function Testing](#system-function-testing)
3. [Demo Scenario Testing](#demo-scenario-testing)
4. [Automated Testing](#automated-testing)
5. [Troubleshooting](#troubleshooting)

---

## 🚀 Quick Start

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: v16.0.0 or higher
- **Memory**: Minimum 4GB, recommended 8GB
- **Storage**: At least 2GB available space
- **Network**: Stable internet connection (for downloading AI models)

### 🎯 Recommended Startup Methods

#### Method 1: Main Console (Recommended)
```bash
# Windows users - Start main console
main-menu.bat

# Provides complete system management interface including:
# • System status check
# • One-click startup
# • Quick demo
# • Complete testing
# • System maintenance
```

#### Method 2: One-Click Startup
```bash
# Windows users
start-system.bat

# Linux/Mac users
chmod +x start-system.sh
./start-system.sh
```

#### Method 3: Quick Demo
```bash
# Windows users - 5-minute quick demo
quick-demo.bat

# Automatically guides through core feature demonstration
```

### Manual Startup Steps
```bash
# 1. Check Node.js version
node --version

# 2. Start Ollama service
ollama serve

# 3. Download AI model (first run)
ollama pull llama3.1:8b

# 4. Install project dependencies
npm install

# 5. Start development server
npm run dev
```

---

## 🔧 System Function Testing

### 1. Basic Function Verification

#### 1.1 System Startup Test
- [ ] Access `http://localhost:5173`
- [ ] Page loads normally without console errors
- [ ] Navigation menu displays correctly
- [ ] All tabs can be switched normally

#### 1.2 AI Assistant Function Test
- [ ] Click "AI Assistant" tab
- [ ] Enter test question: "What is R-32 refrigerant?"
- [ ] Verify AI response contains relevant information
- [ ] Check response time (should be within 5 seconds)
- [ ] Verify source citations display correctly

#### 1.3 Smart Forms Test
- [ ] Click "Smart Forms" tab
- [ ] Create new ticket: "Create New Ticket"
- [ ] Select "Equipment Purchase" category
- [ ] Verify auto-fill functionality
- [ ] Upload test document (PDF/image)
- [ ] Check document parsing results

### 2. Maintenance Dashboard Test

#### 2.1 Equipment Status Monitoring
- [ ] Access "Maintenance Dashboard"
- [ ] View equipment health status
- [ ] Verify alert display (red/yellow indicators)
- [ ] Click equipment to view detailed information
- [ ] Check sensor data display

#### 2.2 Predictive Maintenance
- [ ] Click "Predictive Analytics" tab
- [ ] Select test equipment
- [ ] Click "Analyze" button
- [ ] Verify prediction results display
- [ ] Check cost comparison analysis
- [ ] Confirm ROI calculation is correct

### 3. Advanced Function Testing

#### 3.1 RAG System Test
- [ ] Test complex query: "Complete VRV system maintenance procedure including safety requirements"
- [ ] Verify multi-source document integration
- [ ] Check confidence score ratings
- [ ] Verify expert attribution display
- [ ] Test document viewing function (eye icon)

#### 3.2 Workflow Test
- [ ] Create equipment purchase request
- [ ] Track approval process
- [ ] Verify real-time status updates
- [ ] Check automatic routing function
- [ ] Test notification system

---

## 🎯 Demo Scenario Testing

### Scenario 1: Emergency Breakdown Response (8 minutes)

#### Background Setup
- **Time**: Friday 5:30 PM
- **Equipment**: Compressor A (Production Line 2)
- **Crisis**: Customer order due Monday morning
- **Traditional Cost**: RM75,000
- **AI Solution Cost**: RM5,000

#### Test Steps
1. **Crisis Simulation** (1 minute)
   - [ ] Access maintenance dashboard
   - [ ] Find "HVAC-001" or "COMP-001" equipment
   - [ ] Verify "Critical" status display
   - [ ] Confirm red alerts are visible

2. **AI Response Demo** (3 minutes)
   - [ ] Click faulty equipment
   - [ ] View sensor readings (red indicators)
   - [ ] Verify AI diagnosis: "Hydraulic pressure drop - 73% probability valve failure"
   - [ ] Navigate to AI assistant
   - [ ] Ask: "What is the solution for compressor hydraulic pressure drop?"
   - [ ] Verify expert assignment: "Ahmad Hassan - Available On-site"

3. **Solution Delivery** (2 minutes)
   - [ ] View historical case display (2022)
   - [ ] Verify step-by-step repair guide
   - [ ] Click eye icon to view complete document
   - [ ] Confirm valve location information

4. **Cost Comparison** (2 minutes)
   - [ ] Navigate to predictive analytics tab
   - [ ] Select faulty equipment
   - [ ] Click "Analyze" button
   - [ ] Verify cost comparison:
     - Traditional method: RM75,000, 72+ hours
     - AI method: RM5,000, 15 minutes
     - Net savings: RM70,000

### Scenario 2: New Equipment Purchase Workflow (9 minutes)

#### Background Setup
- **Request**: New injection molding machine for Production Line 3
- **Budget**: RM485,000
- **Traditional Process**: 21 days, 15% error rate
- **AI Process**: 3 days, 0% errors

#### Test Steps
1. **Smart Forms Demo** (3 minutes)
   - [ ] Click "Smart Forms"
   - [ ] Create new request: "Create New Ticket"
   - [ ] Select "Equipment Purchase"
   - [ ] Enter: "New injection molding machine for Production Line 3"
   - [ ] Verify auto-fill: 23 fields
   - [ ] Check budget validation: "RM485K within remaining budget"
   - [ ] Confirm vendor suggestion: "Sumitomo SE-450DU recommended"

2. **Real-time Approval Tracking** (2 minutes)
   - [ ] Submit form
   - [ ] Record ticket ID
   - [ ] Verify status: "IN PROGRESS"
   - [ ] View progress bar
   - [ ] Check approval chain display

3. **Document Intelligence Demo** (2 minutes)
   - [ ] Upload test PDF file
   - [ ] Upload test image
   - [ ] Verify automatic data extraction
   - [ ] Check classification results

### Scenario 3: Predictive Maintenance Intelligence (8 minutes)

#### Background Setup
- **Equipment**: Hydraulic Press B (Production Line 1)
- **Prediction**: Bearing failure in 72 hours (94% confidence)
- **Prevention Cost**: RM15,000
- **Breakdown Cost**: RM85,000

#### Test Steps
1. **Predictive Detection** (2 minutes)
   - [ ] Navigate to Maintenance Dashboard → Predictive Analytics
   - [ ] View 12 equipment health scores
   - [ ] Find amber warning equipment (Health Score: 73%)
   - [ ] Click detailed analysis
   - [ ] Verify predictive alert display

2. **Automated Scheduling** (2 minutes)
   - [ ] View automatically generated maintenance request
   - [ ] Verify technician availability check
   - [ ] Confirm optimal maintenance window
   - [ ] Check automatic parts ordering

3. **Cost Impact Analysis** (2 minutes)
   - [ ] View visual comparison
   - [ ] Verify preventive: RM15,000, 6 hours planned downtime
   - [ ] Verify breakdown: RM85,000, 72+ hours unplanned downtime
   - [ ] Confirm ROI calculation: 233%-350%

---

## ⚡ Automated Testing

### 🤖 One-Click Test Suite

#### Complete Automated Testing
```bash
# Windows users
run-tests.bat

# Linux/Mac users
chmod +x run-tests.sh
./run-tests.sh
```

**Test Content:**
- ✅ Basic connection test
- ✅ AI service connection test  
- ✅ API endpoint test
- ✅ Performance benchmark test
- ✅ System resource check

**Auto-Generated Reports:**
- 📄 Saved to `test-results/test-report-timestamp.txt`
- 📊 Includes detailed performance metrics
- 🔍 Provides problem diagnosis information

#### System Status Check
```bash
# Windows users
check-system.bat

# Checks all system dependencies and service status
```

### 📊 Performance Testing

#### 1. Response Time Testing
```bash
# Automated performance testing (included in run-tests script)
# Manual performance testing
curl -w "Response Time: %{time_total}s\n" http://localhost:5173
```

**Test Metrics:**
- [ ] AI query response time < 5 seconds
- [ ] Page load time < 3 seconds
- [ ] Document upload processing < 10 seconds
- [ ] Database query < 1 second

#### 2. Concurrent User Testing
- [ ] Simulate 5 concurrent users
- [ ] Submit AI queries simultaneously
- [ ] Verify response quality consistency
- [ ] Check system stability

#### 3. Large Data Volume Testing
- [ ] Test large knowledge base performance
- [ ] Verify search relevance
- [ ] Check memory usage
- [ ] Monitor CPU utilization

---

## 🛠️ Troubleshooting

### Common Issues

#### 1. Ollama Connection Failed
```bash
# Check Ollama status
curl http://localhost:11434/api/tags

# Restart Ollama service
ollama serve

# Check model list
ollama list
```

#### 2. Port Conflict
```bash
# Check port usage
netstat -ano | findstr :5173  # Windows
lsof -i :5173                 # Linux/Mac

# Change port
npm run dev -- --port 3000
```

#### 3. Dependency Installation Issues
```bash
# Clear cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### 4. AI Model Issues
```bash
# Re-download model
ollama rm llama3.1:8b
ollama pull llama3.1:8b

# Check model integrity
ollama show llama3.1:8b
```

### Performance Optimization Recommendations

1. **System Resources**
   - Ensure at least 8GB RAM available
   - Close unnecessary background programs
   - Use SSD storage for improved I/O performance

2. **Network Optimization**
   - Use stable network connection
   - Consider local AI model caching
   - Optimize API request frequency

3. **Browser Optimization**
   - Use latest Chrome or Edge
   - Clear browser cache
   - Disable unnecessary extensions

---

## 📊 Test Report Template

### Test Execution Record
- **Test Date**: ___________
- **Tester**: ___________
- **System Version**: ___________
- **Test Environment**: ___________

### Function Test Results
| Function Module | Test Case | Status | Notes |
|---------|---------|------|------|
| AI Assistant | Basic Query | ✅/❌ | |
| Smart Forms | Auto-fill | ✅/❌ | |
| Maintenance Dashboard | Equipment Monitoring | ✅/❌ | |
| Predictive Analytics | Cost Calculation | ✅/❌ | |

### Performance Test Results
| Metric | Target Value | Actual Value | Status |
|------|--------|--------|------|
| AI Response Time | <5s | ___s | ✅/❌ |
| Page Load | <3s | ___s | ✅/❌ |
| Concurrent Processing | 5 users | ___users | ✅/❌ |

### Issue Record
1. **Issue Description**: ___________
   - **Severity**: High/Medium/Low
   - **Reproduction Steps**: ___________
   - **Solution**: ___________

---

## 🎯 Success Criteria

### Demo Success Indicators
- [ ] All three demo scenarios completed smoothly
- [ ] AI responses accurate and timely
- [ ] Cost savings calculations display correctly
- [ ] User interface responds smoothly
- [ ] No system errors or crashes

### Business Value Validation
- [ ] Emergency response: 15 minutes vs 72+ hours
- [ ] Workflow efficiency: 3 days vs 21 days
- [ ] Predictive maintenance: 467% ROI
- [ ] Overall savings: RM450K annual savings

**Remember**: The goal is to prove financial value, not to show off technology. Every demo element should reinforce the RM240K → RM450K value proposition.
