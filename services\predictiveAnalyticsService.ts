/**
 * Predictive Analytics Service
 * Provides failure prediction and pattern recognition based on historical data
 */

import type { Machine, Alert, Ticket } from '../types';

export interface PredictiveAnalysis {
    prediction_type: 'failure_prediction' | 'maintenance_recommendation' | 'pattern_analysis';
    confidence_score: number;
    predicted_failure_date?: string;
    failure_probability: number;
    historical_patterns: HistoricalPattern[];
    recommended_actions: RecommendedAction[];
    expert_insights: ExpertInsight[];
    similar_cases: SimilarCase[];
}

export interface HistoricalPattern {
    pattern_id: string;
    description: string;
    frequency: number;
    last_occurrence: string;
    success_rate: number;
    related_machines: string[];
}

export interface RecommendedAction {
    action_id: string;
    description: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    estimated_time: string;
    success_probability: number;
    required_expertise: string[];
}

export interface ExpertInsight {
    expert_name: string;
    insight: string;
    confidence_level: number;
    contact_info: string;
    expertise_areas: string[];
    case_references: string[];
}

export interface SimilarCase {
    case_id: string;
    machine_type: string;
    symptoms: string[];
    resolution: string;
    resolution_time: string;
    success_rate: number;
    lessons_learned: string;
}

// Simulate historical maintenance data
const HISTORICAL_MAINTENANCE_DATA = {
    patterns: [
        {
            pattern_id: 'HVAC_FILTER_DEGRADATION',
            description: 'HVAC filter degradation pattern leading to efficiency loss',
            frequency: 85,
            last_occurrence: '2024-01-15',
            success_rate: 95,
            related_machines: ['HVAC-001', 'HVAC-002', 'HVAC-003']
        },
        {
            pattern_id: 'COMPRESSOR_VIBRATION_INCREASE',
            description: 'Gradual increase in compressor vibration indicating bearing wear',
            frequency: 72,
            last_occurrence: '2024-02-03',
            success_rate: 88,
            related_machines: ['COMP-001', 'COMP-002']
        },
        {
            pattern_id: 'TEMPERATURE_FLUCTUATION',
            description: 'Temperature control fluctuations indicating sensor drift',
            frequency: 65,
            last_occurrence: '2024-01-28',
            success_rate: 92,
            related_machines: ['HVAC-001', 'HVAC-004']
        }
    ],
    similar_cases: [
        {
            case_id: 'CASE_2023_087',
            machine_type: 'HVAC Unit',
            symptoms: ['Reduced cooling efficiency', 'Unusual noise', 'Higher energy consumption'],
            resolution: 'Replaced compressor and cleaned evaporator coils',
            resolution_time: '4 hours',
            success_rate: 100,
            lessons_learned: 'Early detection through vibration monitoring prevented major failure'
        },
        {
            case_id: 'CASE_2023_124',
            machine_type: 'Compressor',
            symptoms: ['Vibration increase', 'Temperature rise', 'Pressure fluctuation'],
            resolution: 'Bearing replacement and alignment adjustment',
            resolution_time: '6 hours',
            success_rate: 95,
            lessons_learned: 'Regular vibration analysis is crucial for early detection'
        }
    ],
    expert_insights: [
        {
            expert_name: 'Ahmad Hassan',
            insight: 'Based on 15 years of experience, this pattern typically indicates bearing wear. Immediate action recommended.',
            confidence_level: 95,
            contact_info: '<EMAIL>',
            expertise_areas: ['HVAC Systems', 'Predictive Maintenance', 'Vibration Analysis'],
            case_references: ['CASE_2023_087', 'CASE_2022_156']
        },
        {
            expert_name: 'Sarah Chen',
            insight: 'Temperature fluctuations of this magnitude suggest sensor calibration issues. Recommend immediate sensor check.',
            confidence_level: 88,
            contact_info: '<EMAIL>',
            expertise_areas: ['Temperature Control', 'Sensor Technology', 'System Diagnostics'],
            case_references: ['CASE_2023_124', 'CASE_2023_089']
        }
    ]
};

/**
 * Analyze machine data and predict potential failures
 */
export const predictMachineFailure = async (
    machine: Machine,
    historicalTickets: Ticket[],
    currentAlerts: Alert[]
): Promise<PredictiveAnalysis> => {
    // Simulate analysis delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Analyze machine condition based on status and alerts
    const machineAlerts = currentAlerts.filter(alert => {
        if (!alert?.message || !machine?.name || !machine?.machineType) return false;
        const alertMessage = alert.message.toLowerCase();
        const machineName = machine.name.toLowerCase();
        const machineType = machine.machineType.toLowerCase();
        return alertMessage.includes(machineName) || alertMessage.includes(machineType);
    });

    const relatedTickets = historicalTickets.filter(ticket => {
        if (!ticket?.description || !machine?.name || !machine?.machineType) return false;
        const ticketDescription = ticket.description.toLowerCase();
        const machineName = machine.name.toLowerCase();
        const machineType = machine.machineType.toLowerCase();
        return ticketDescription.includes(machineName) || ticketDescription.includes(machineType);
    });

    // Calculate failure probability based on multiple factors
    let failureProbability = 0;
    
    // Factor 1: Machine status
    if (machine.status === 'Failure') failureProbability += 0.4;
    else if (machine.status === 'Warning') failureProbability += 0.2;

    // Factor 2: Number of alerts
    failureProbability += Math.min(machineAlerts.length * 0.1, 0.3);

    // Factor 3: Historical ticket frequency
    failureProbability += Math.min(relatedTickets.length * 0.05, 0.2);

    // Factor 4: Machine age (simulated)
    const machineAge = Math.random() * 10; // 0-10 years
    failureProbability += Math.min(machineAge * 0.02, 0.1);

    // Ensure probability is between 0 and 1
    failureProbability = Math.min(failureProbability, 0.95);

    // Determine prediction type based on failure probability
    let predictionType: PredictiveAnalysis['prediction_type'];
    if (failureProbability > 0.7) predictionType = 'failure_prediction';
    else if (failureProbability > 0.3) predictionType = 'maintenance_recommendation';
    else predictionType = 'pattern_analysis';

    // Calculate predicted failure date
    const daysUntilFailure = Math.max(1, Math.floor((1 - failureProbability) * 90));
    const predictedFailureDate = new Date();
    predictedFailureDate.setDate(predictedFailureDate.getDate() + daysUntilFailure);

    // Select relevant historical patterns
    const relevantPatterns = HISTORICAL_MAINTENANCE_DATA.patterns.filter(pattern =>
        pattern.related_machines.some(machineId =>
            machineId.includes(machine.machineType.substring(0, 4).toUpperCase())
        )
    );

    // Generate recommended actions based on failure probability
    const recommendedActions: RecommendedAction[] = [];
    
    if (failureProbability > 0.7) {
        recommendedActions.push({
            action_id: 'IMMEDIATE_INSPECTION',
            description: 'Immediate comprehensive inspection and diagnostic testing',
            priority: 'critical',
            estimated_time: '2-4 hours',
            success_probability: 95,
            required_expertise: ['Senior Technician', 'Diagnostic Specialist']
        });
    }

    if (failureProbability > 0.5) {
        recommendedActions.push({
            action_id: 'PREVENTIVE_MAINTENANCE',
            description: 'Schedule preventive maintenance within 7 days',
            priority: 'high',
            estimated_time: '4-6 hours',
            success_probability: 88,
            required_expertise: ['Maintenance Technician']
        });
    }

    recommendedActions.push({
        action_id: 'MONITORING_INCREASE',
        description: 'Increase monitoring frequency and set up automated alerts',
        priority: 'medium',
        estimated_time: '1 hour',
        success_probability: 75,
        required_expertise: ['System Administrator']
    });

    // Select relevant expert insights
    const relevantExperts = HISTORICAL_MAINTENANCE_DATA.expert_insights.filter(expert =>
        expert.expertise_areas.some(area => 
            machine.type.toLowerCase().includes(area.toLowerCase().split(' ')[0])
        )
    );

    // Select similar cases
    const similarCases = HISTORICAL_MAINTENANCE_DATA.similar_cases.filter(case_ =>
        case_.machine_type.toLowerCase().includes(machine.type.toLowerCase())
    );

    return {
        prediction_type: predictionType,
        confidence_score: Math.round((0.7 + Math.random() * 0.25) * 100) / 100,
        predicted_failure_date: predictedFailureDate.toISOString().split('T')[0],
        failure_probability: Math.round(failureProbability * 100) / 100,
        historical_patterns: relevantPatterns,
        recommended_actions: recommendedActions,
        expert_insights: relevantExperts,
        similar_cases: similarCases
    };
};

/**
 * Analyze patterns across multiple machines
 */
export const analyzeSystemPatterns = async (
    machines: Machine[],
    historicalTickets: Ticket[],
    alerts: Alert[]
): Promise<{
    system_health_score: number;
    trending_issues: string[];
    risk_assessment: {
        high_risk_machines: string[];
        medium_risk_machines: string[];
        low_risk_machines: string[];
    };
    recommendations: string[];
}> => {
    await new Promise(resolve => setTimeout(resolve, 1000));

    const criticalMachines = machines.filter(m => m.status === 'Critical').length;
    const warningMachines = machines.filter(m => m.status === 'Warning').length;
    const totalMachines = machines.length;

    // Calculate system health score
    const systemHealthScore = Math.max(0, 100 - (criticalMachines * 25) - (warningMachines * 10));

    // Identify trending issues
    const trendingIssues = [
        'HVAC efficiency degradation',
        'Compressor vibration increase',
        'Temperature control fluctuations'
    ];

    // Risk assessment
    const highRiskMachines = machines.filter(m => m.status === 'Critical').map(m => m.name);
    const mediumRiskMachines = machines.filter(m => m.status === 'Warning').map(m => m.name);
    const lowRiskMachines = machines.filter(m => m.status === 'Operational').map(m => m.name);

    const recommendations = [
        'Schedule immediate inspection for critical machines',
        'Implement predictive maintenance program',
        'Increase monitoring frequency for warning-status machines',
        'Review and update maintenance schedules'
    ];

    return {
        system_health_score: systemHealthScore,
        trending_issues: trendingIssues,
        risk_assessment: {
            high_risk_machines: highRiskMachines,
            medium_risk_machines: mediumRiskMachines,
            low_risk_machines: lowRiskMachines
        },
        recommendations
    };
};

/**
 * Generate expert recommendations based on machine analysis
 */
export const generateExpertRecommendations = (
    analysis: PredictiveAnalysis,
    machineType: string
): ExpertInsight[] => {
    return analysis.expert_insights.map(insight => ({
        ...insight,
        insight: `For ${machineType}: ${insight.insight}`,
        case_references: insight.case_references.slice(0, 2) // Limit references
    }));
};
