
import React, { useState, FC, useContext, useMemo } from 'react';
import { AppContext } from './AppContext';
import type { ProductCategory, QuickLink, KnowledgeArticle, Announcement, Policy, WikiAttachment } from '../types';
import AttachmentAnalysisWorkflow from './AttachmentAnalysisWorkflow';
import {
    HomeIcon,
    CircleStackIcon,
    BuildingOfficeIcon,
    UserGroupIcon,
    BeakerIcon,
    BookOpenIcon,
    SearchIcon,
    TicketIcon,
    ClipboardDocumentListIcon,
    PaperclipIcon,
    MegaphoneIcon,
    WrenchScrewdriverIcon,
    BotIcon,
    PlusIcon,
    PencilIcon,
    XMarkIcon,
    DocumentIcon,
    TrashIcon,
    EyeIcon
} from './icons/Icons';

type WikiView = 'home' | 'products' | 'company' | 'hr' | 'rd' | 'manage';
type IconElement = React.ReactElement<React.SVGProps<SVGSVGElement>>;

const iconMap: { [key: string]: React.FC<React.SVGProps<SVGSVGElement>> } = {
    UserGroupIcon,
    PaperclipIcon,
    TicketIcon,
    ClipboardDocumentListIcon,
    WrenchScrewdriverIcon,
    BotIcon,
};

// --- Modal for Document Simulation ---
const DocumentViewerModal: FC<{ docName: string, onClose: () => void }> = ({ docName, onClose }) => (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50" role="dialog" aria-modal="true">
        <div className="absolute inset-0" onClick={onClose}></div>
        <div className="relative bg-white rounded-lg shadow-xl p-8 max-w-2xl w-full border border-gray-200">
             <h3 className="text-lg font-bold text-gray-900 flex items-center gap-2"><BookOpenIcon className="w-5 h-5"/> Document Viewer</h3>
             <p className="text-gray-600 my-4">Displaying document: <span className="font-semibold text-gray-800">{docName}</span></p>
             <div className="bg-gray-100 h-64 rounded-md border border-gray-200 flex items-center justify-center text-gray-400">
                <p>(Simulated document content)</p>
             </div>
            <div className="flex justify-end gap-4 mt-6">
                <button onClick={onClose} className="px-4 py-2 text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 rounded-lg">Close Viewer</button>
            </div>
        </div>
    </div>
);


// --- Sub-Components for Different Wiki Views ---

const WikiHome: FC = () => {
    const { wikiAnnouncements, wikiQuickLinks, setActiveView } = useContext(AppContext);
    
    const handleLinkClick = (link: QuickLink) => {
        if(link.appTarget) {
            setActiveView(link.appTarget);
        }
    };
    
    return (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2"><MegaphoneIcon className="w-5 h-5 text-gray-500" /> Latest Announcements</h3>
                <div className="space-y-4">
                    {wikiAnnouncements.map(announcement => (
                        <div key={announcement.id} className="bg-white border border-gray-200 rounded-lg p-4">
                            <h4 className="font-bold text-gray-800">{announcement.title}</h4>
                            <p className="text-xs text-gray-500 mb-2">By {announcement.author} on {announcement.date}</p>
                            <p className="text-sm text-gray-600">{announcement.content}</p>
                        </div>
                    ))}
                </div>
            </div>
            <div>
                 <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Links</h3>
                 <div className="space-y-3">
                     {wikiQuickLinks.map(link => {
                         const Icon = iconMap[link.icon];
                         const isExternal = !!link.url;
                         return(
                            <a href={isExternal ? link.url : undefined} onClick={!isExternal ? () => handleLinkClick(link) : undefined} key={link.id} className="flex items-center gap-3 bg-white border border-gray-200 rounded-lg p-3 hover:bg-gray-100 hover:border-gray-300 transition-colors cursor-pointer">
                                {Icon && <Icon className="w-5 h-5 text-blue-600" />}
                                <span className="text-sm font-medium text-gray-700">{link.title}</span>
                            </a>
                        );
                     })}
                 </div>
            </div>
        </div>
    );
};

const ProductKnowledgeBase: FC<{ onDocumentClick: (docName: string) => void }> = ({ onDocumentClick }) => {
    const { wikiProductManuals } = useContext(AppContext);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<ProductCategory | 'All'>('All');

    const categories = useMemo(() => ['All', ...new Set(wikiProductManuals.map(p => p.category))] as const, [wikiProductManuals]);
    
    const filteredManuals = useMemo(() => {
        return wikiProductManuals.filter(manual => {
            const matchesCategory = selectedCategory === 'All' || manual.category === selectedCategory;
            const matchesSearch = searchTerm === '' || 
                                  manual.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                                  manual.modelNumbers.some(m => m.toLowerCase().includes(searchTerm.toLowerCase()));
            return matchesCategory && matchesSearch;
        });
    }, [wikiProductManuals, searchTerm, selectedCategory]);

    return (
        <div>
            <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6 flex items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                    {categories.map(cat => (
                        <button key={cat} onClick={() => setSelectedCategory(cat)} className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${selectedCategory === cat ? 'bg-blue-600 text-white' : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-100'}`}>
                           {cat}
                        </button>
                    ))}
                </div>
                <div className="relative">
                    <SearchIcon className="w-4 h-4 text-gray-400 absolute top-1/2 left-3 -translate-y-1/2" />
                    <input 
                        type="text"
                        placeholder="Search manuals..."
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                        className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md focus:ring-blue-500 focus:border-blue-500 block w-full pl-9 p-2"
                    />
                </div>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                 <table className="w-full text-sm text-left text-gray-500">
                    <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th className="px-6 py-3">Product Name</th>
                            <th className="px-6 py-3">Category</th>
                            <th className="px-6 py-3">Summary</th>
                            <th className="px-6 py-3">Document</th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                        {filteredManuals.map(manual => (
                            <tr key={manual.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 font-medium text-gray-900">{manual.name}</td>
                                <td className="px-6 py-4">{manual.category}</td>
                                <td className="px-6 py-4">{manual.summary}</td>
                                <td className="px-6 py-4">
                                    <button onClick={() => onDocumentClick(manual.name)} className="text-blue-600 hover:underline flex items-center gap-1">
                                        <BookOpenIcon className="w-4 h-4" /> View Manual
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                 </table>
            </div>
        </div>
    );
};

const HrPolicies: FC<{ onDocumentClick: (docName: string) => void }> = ({ onDocumentClick }) => {
    const { wikiPolicies } = useContext(AppContext);
    return (
        <div className="space-y-4">
            {wikiPolicies.map(policy => (
                 <div key={policy.id} className="bg-white rounded-lg border border-gray-200 p-4 flex items-center justify-between">
                     <div>
                        <p className="text-xs text-gray-500">{policy.category}</p>
                        <h4 className="font-bold text-gray-800 mt-1">{policy.title}</h4>
                        <p className="text-sm text-gray-600 mt-2">{policy.summary}</p>
                     </div>
                     <button onClick={() => onDocumentClick(policy.title)} className="flex-shrink-0 ml-6 px-4 py-2 text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 rounded-lg">View Policy</button>
                 </div>
            ))}
        </div>
    );
};

const ArticleView: FC<{ articles: KnowledgeArticle[] }> = ({ articles }) => {
    return (
        <div className="space-y-8">
            {articles.map(article => (
                <article key={article.id} className="bg-white rounded-lg border border-gray-200 p-6">
                    <header className="mb-4">
                        <p className="text-sm font-semibold text-violet-600">{article.category}</p>
                        <h1 className="text-2xl font-bold text-gray-900 mt-1">{article.title}</h1>
                        <p className="text-sm text-gray-500 mt-2">By {article.author} &middot; Published on {article.publishDate}</p>
                    </header>
                    <div className="prose prose-sm max-w-none text-gray-600 whitespace-pre-wrap leading-relaxed">
                        {article.content}
                    </div>
                    <footer className="mt-6 pt-4 border-t border-gray-200">
                        <div className="flex flex-wrap gap-2">
                            {article.tags.map(tag => (
                                <span key={tag} className="text-xs px-2.5 py-1 bg-gray-100 text-gray-700 rounded-full">{tag}</span>
                            ))}
                        </div>
                    </footer>
                </article>
            ))}
             {articles.length === 0 && (
                <div className="text-center py-16 text-gray-400">
                    <BookOpenIcon className="w-12 h-12 mx-auto mb-4"/>
                    <h3 className="font-semibold text-gray-500">No Articles Found</h3>
                    <p>There are no knowledge articles in this category yet.</p>
                </div>
            )}
        </div>
    );
};

// --- Wiki Management Component ---
const WikiManagement: React.FC = () => {
    const {
        wikiKnowledgeArticles,
        wikiAnnouncements,
        wikiPolicies,
        addKnowledgeArticle,
        updateKnowledgeArticle,
        deleteKnowledgeArticle,
        addAnnouncement,
        updateAnnouncement,
        deleteAnnouncement,
        addPolicy,
        updatePolicy,
        deletePolicy,
        currentUser
    } = useContext(AppContext);

    const [activeTab, setActiveTab] = useState<'articles' | 'announcements' | 'policies'>('articles');
    const [editingItem, setEditingItem] = useState<any>(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
    const [isCreating, setIsCreating] = useState(false);
    const [showAttachmentAnalysis, setShowAttachmentAnalysis] = useState(false);
    const [pendingFile, setPendingFile] = useState<File | null>(null);

    const handleCreate = () => {
        const newItem = activeTab === 'articles' ? {
            id: `art-${Date.now()}`,
            title: 'New Article',
            content: 'Article content...',
            category: 'General',
            tags: [],
            author: currentUser.name,
            authorDepartment: currentUser.department,
            publishDate: new Date().toISOString().split('T')[0],
            attachments: []
        } : activeTab === 'announcements' ? {
            id: `ann-${Date.now()}`,
            title: 'New Announcement',
            content: 'Announcement content...',
            priority: 'Medium' as const,
            author: currentUser.name,
            authorDepartment: currentUser.department,
            date: new Date().toISOString().split('T')[0],
            attachments: []
        } : {
            id: `pol-${Date.now()}`,
            title: 'New Policy',
            summary: 'Policy content...',
            category: 'General',
            author: currentUser.name,
            authorDepartment: currentUser.department,
            effectiveDate: new Date().toISOString().split('T')[0],
            lastUpdated: new Date().toISOString().split('T')[0],
            documentUrl: '',
            attachments: []
        };

        setEditingItem(newItem);
        setIsCreating(true);
    };

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setPendingFile(file);
            setShowAttachmentAnalysis(true);
        }
    };

    const handleAttachmentAnalysisComplete = (attachment: WikiAttachment) => {
        if (editingItem) {
            setEditingItem({
                ...editingItem,
                attachments: [...(editingItem.attachments || []), attachment]
            });
        }
        setShowAttachmentAnalysis(false);
        setPendingFile(null);
    };

    const handleRemoveAttachment = (attachmentId: string) => {
        if (editingItem) {
            setEditingItem({
                ...editingItem,
                attachments: editingItem.attachments?.filter((att: WikiAttachment) => att.id !== attachmentId) || []
            });
        }
    };

    const handleSave = () => {
        if (!editingItem) return;

        if (isCreating) {
            if (activeTab === 'articles') addKnowledgeArticle(editingItem);
            else if (activeTab === 'announcements') addAnnouncement(editingItem);
            else if (activeTab === 'policies') addPolicy(editingItem);
        } else {
            if (activeTab === 'articles') updateKnowledgeArticle(editingItem);
            else if (activeTab === 'announcements') updateAnnouncement(editingItem);
            else if (activeTab === 'policies') updatePolicy(editingItem);
        }

        setEditingItem(null);
        setIsCreating(false);
    };

    const handleDelete = (id: string) => {
        if (activeTab === 'articles') deleteKnowledgeArticle(id);
        else if (activeTab === 'announcements') deleteAnnouncement(id);
        else if (activeTab === 'policies') deletePolicy(id);
        setShowDeleteConfirm(null);
    };

    const getCurrentItems = () => {
        switch (activeTab) {
            case 'articles': return wikiKnowledgeArticles;
            case 'announcements': return wikiAnnouncements;
            case 'policies': return wikiPolicies;
            default: return [];
        }
    };

    if (editingItem) {
        return (
            <div className="p-6 max-w-4xl mx-auto">
                <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-gray-900">
                        {isCreating ? 'Create' : 'Edit'} {activeTab.slice(0, -1).charAt(0).toUpperCase() + activeTab.slice(1, -1)}
                    </h3>
                    <div className="flex gap-2">
                        <button
                            onClick={() => { setEditingItem(null); setIsCreating(false); }}
                            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={handleSave}
                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                        >
                            Save
                        </button>
                    </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6 space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                        <input
                            type="text"
                            value={editingItem.title}
                            onChange={(e) => setEditingItem({ ...editingItem, title: e.target.value })}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Content</label>
                        <textarea
                            value={editingItem.content}
                            onChange={(e) => setEditingItem({ ...editingItem, content: e.target.value })}
                            rows={8}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>

                    {activeTab === 'articles' && (
                        <>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                <select
                                    value={editingItem.category}
                                    onChange={(e) => setEditingItem({ ...editingItem, category: e.target.value })}
                                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="General">General</option>
                                    <option value="Company Philosophy">Company Philosophy</option>
                                    <option value="R&D">R&D</option>
                                    <option value="Engineering Best Practices">Engineering Best Practices</option>
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Tags (comma separated)</label>
                                <input
                                    type="text"
                                    value={editingItem.tags?.join(', ') || ''}
                                    onChange={(e) => setEditingItem({ ...editingItem, tags: e.target.value.split(',').map(t => t.trim()).filter(t => t) })}
                                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                        </>
                    )}

                    {activeTab === 'announcements' && (
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                            <select
                                value={editingItem.priority}
                                onChange={(e) => setEditingItem({ ...editingItem, priority: e.target.value })}
                                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="Low">Low</option>
                                <option value="Medium">Medium</option>
                                <option value="High">High</option>
                            </select>
                        </div>
                    )}

                    {activeTab === 'policies' && (
                        <>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                <input
                                    type="text"
                                    value={editingItem.category}
                                    onChange={(e) => setEditingItem({ ...editingItem, category: e.target.value })}
                                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Effective Date</label>
                                <input
                                    type="date"
                                    value={editingItem.effectiveDate}
                                    onChange={(e) => setEditingItem({ ...editingItem, effectiveDate: e.target.value })}
                                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                        </>
                    )}

                    {/* Attachments Section */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Attachments</label>

                        {/* Upload Button */}
                        <div className="mb-4">
                            <input
                                type="file"
                                id="file-upload"
                                className="hidden"
                                onChange={handleFileUpload}
                                accept=".pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.json,.xml"
                            />
                            <label
                                htmlFor="file-upload"
                                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 cursor-pointer border border-blue-200"
                            >
                                <PaperclipIcon className="w-4 h-4" />
                                Upload Attachment
                            </label>
                        </div>

                        {/* Existing Attachments */}
                        {editingItem.attachments && editingItem.attachments.length > 0 && (
                            <div className="space-y-2">
                                {editingItem.attachments.map((attachment: WikiAttachment) => (
                                    <div key={attachment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
                                        <div className="flex items-center gap-3">
                                            <DocumentIcon className="w-5 h-5 text-blue-600" />
                                            <div>
                                                <p className="font-medium text-gray-900">{attachment.name}</p>
                                                <div className="flex items-center gap-2 text-xs text-gray-500">
                                                    <span>{(attachment.size / 1024).toFixed(1)} KB</span>
                                                    <span>•</span>
                                                    <span className={`px-2 py-1 rounded-full ${
                                                        attachment.dataType === 'structured'
                                                            ? 'bg-blue-100 text-blue-700'
                                                            : 'bg-purple-100 text-purple-700'
                                                    }`}>
                                                        {attachment.dataType === 'structured' ? '📊 Structured' : '📄 Unstructured'}
                                                    </span>
                                                    {attachment.analysisResult && (
                                                        <>
                                                            <span>•</span>
                                                            <span>Confidence: {(attachment.analysisResult.confidence * 100).toFixed(0)}%</span>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <button
                                                onClick={() => window.open(attachment.url, '_blank')}
                                                className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                                                title="View attachment"
                                            >
                                                <EyeIcon className="w-4 h-4" />
                                            </button>
                                            <button
                                                onClick={() => handleRemoveAttachment(attachment.id)}
                                                className="p-1 text-red-600 hover:bg-red-50 rounded"
                                                title="Remove attachment"
                                            >
                                                <TrashIcon className="w-4 h-4" />
                                            </button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Author Information Display */}
                    <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-2">Author Information</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span className="text-gray-600">Author:</span>
                                <span className="ml-2 font-medium text-gray-900">{editingItem.author}</span>
                            </div>
                            <div>
                                <span className="text-gray-600">Department:</span>
                                <span className="ml-2 font-medium text-gray-900">{editingItem.authorDepartment}</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Attachment Analysis Modal */}
                {showAttachmentAnalysis && pendingFile && (
                    <AttachmentAnalysisWorkflow
                        file={pendingFile}
                        onAnalysisComplete={handleAttachmentAnalysisComplete}
                        onCancel={() => {
                            setShowAttachmentAnalysis(false);
                            setPendingFile(null);
                        }}
                    />
                )}
            </div>
        );
    }

    return (
        <div className="p-6">
            <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Manage Wiki Content</h3>
                <button
                    onClick={handleCreate}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
                >
                    <PlusIcon className="w-4 h-4" />
                    Create New
                </button>
            </div>

            {/* Tab Navigation */}
            <div className="flex border-b border-gray-200 mb-6">
                {[
                    { key: 'articles', label: 'Knowledge Articles' },
                    { key: 'announcements', label: 'Announcements' },
                    { key: 'policies', label: 'Policies' }
                ].map(tab => (
                    <button
                        key={tab.key}
                        onClick={() => setActiveTab(tab.key as any)}
                        className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
                            activeTab === tab.key
                                ? 'border-blue-600 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                    >
                        {tab.label}
                    </button>
                ))}
            </div>

            {/* Content List */}
            <div className="space-y-4">
                {getCurrentItems().map((item: any) => (
                    <div key={item.id} className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start justify-between">
                            <div className="flex-1">
                                <h4 className="font-medium text-gray-900">{item.title}</h4>
                                <p className="text-sm text-gray-600 mt-1 line-clamp-2">{item.content || item.summary}</p>

                                {/* Author and Department Info */}
                                <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                                    {item.author && (
                                        <div className="flex items-center gap-1">
                                            <UserGroupIcon className="w-3 h-3" />
                                            <span>By {item.author}</span>
                                            {item.authorDepartment && (
                                                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full ml-1">
                                                    {item.authorDepartment}
                                                </span>
                                            )}
                                        </div>
                                    )}
                                    {item.category && <span>Category: {item.category}</span>}
                                    {item.priority && <span>Priority: {item.priority}</span>}
                                    <span>Updated: {item.lastUpdated || item.date || item.publishDate}</span>
                                </div>

                                {/* Attachments Info */}
                                {item.attachments && item.attachments.length > 0 && (
                                    <div className="flex items-center gap-2 mt-2">
                                        <PaperclipIcon className="w-3 h-3 text-gray-400" />
                                        <span className="text-xs text-gray-500">
                                            {item.attachments.length} attachment{item.attachments.length > 1 ? 's' : ''}
                                        </span>
                                        <div className="flex gap-1">
                                            {item.attachments.slice(0, 3).map((att: WikiAttachment) => (
                                                <span
                                                    key={att.id}
                                                    className={`px-1.5 py-0.5 text-xs rounded ${
                                                        att.dataType === 'structured'
                                                            ? 'bg-blue-100 text-blue-600'
                                                            : 'bg-purple-100 text-purple-600'
                                                    }`}
                                                >
                                                    {att.dataType === 'structured' ? '📊' : '📄'}
                                                </span>
                                            ))}
                                            {item.attachments.length > 3 && (
                                                <span className="text-xs text-gray-400">+{item.attachments.length - 3}</span>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                            <div className="flex gap-2 ml-4">
                                <button
                                    onClick={() => setEditingItem({ ...item })}
                                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-md"
                                >
                                    <PencilIcon className="w-4 h-4" />
                                </button>
                                <button
                                    onClick={() => setShowDeleteConfirm(item.id)}
                                    className="p-2 text-red-600 hover:bg-red-50 rounded-md"
                                >
                                    <XMarkIcon className="w-4 h-4" />
                                </button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Delete Confirmation Modal */}
            {showDeleteConfirm && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                        <h3 className="text-lg font-bold text-gray-900 mb-2">Delete Item</h3>
                        <p className="text-gray-600 mb-4">Are you sure you want to delete this item? This action cannot be undone.</p>
                        <div className="flex gap-2 justify-end">
                            <button
                                onClick={() => setShowDeleteConfirm(null)}
                                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={() => handleDelete(showDeleteConfirm)}
                                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

// --- Main Wiki Component ---
const Wiki: React.FC = () => {
  const { wikiKnowledgeArticles } = useContext(AppContext);
  const [activeView, setActiveView] = useState<WikiView>('home');
  const [viewingDocument, setViewingDocument] = useState<string | null>(null);

  const handleDocumentClick = (docName: string) => {
      setViewingDocument(docName);
  }

  const renderContent = () => {
      switch(activeView) {
          case 'home': return <WikiHome />;
          case 'products': return <ProductKnowledgeBase onDocumentClick={handleDocumentClick}/>;
          case 'company': return <ArticleView articles={wikiKnowledgeArticles.filter(a => a.category === 'Company Philosophy')} />;
          case 'hr': return <HrPolicies onDocumentClick={handleDocumentClick}/>;
          case 'rd': return <ArticleView articles={wikiKnowledgeArticles.filter(a => a.category === 'R&D' || a.category === 'Engineering Best Practices')} />;
          case 'manage': return <WikiManagement />;
          default: return null;
      }
  };
  
  const viewTitles: Record<WikiView, {title: string, subtitle: string}> = {
      home: { title: "Knowledge Brain Home", subtitle: "Welcome to the central hub for all company knowledge and announcements." },
      products: { title: "Product & Services Knowledge Base", subtitle: "Find manuals, technical specifications, and guides for all Daikin products." },
      company: { title: "Company & Culture", subtitle: "Learn about our history, management philosophy, and corporate social responsibility." },
      hr: { title: "Human Resources", subtitle: "Access employee handbooks, policies, and benefits information." },
      rd: { title: "R&D and Technology", subtitle: "Explore technical whitepapers, best practices, and innovation news." },
      manage: { title: "Manage Wiki Content", subtitle: "Create, edit, and manage knowledge articles, announcements, and policies." },
  }

  return (
    <div className="flex-1 flex flex-col text-gray-800 bg-gray-50 overflow-hidden">
      <header className="px-6 py-4 border-b border-gray-200 bg-white flex-shrink-0">
        <h2 className="text-xl font-semibold text-gray-900">{viewTitles[activeView].title}</h2>
        <p className="text-sm text-gray-500">{viewTitles[activeView].subtitle}</p>
      </header>
      <div className="flex-1 flex overflow-hidden">
          <aside className="w-64 bg-white border-r border-gray-200 p-4 flex flex-col">
              <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wider px-2 mb-3">WIKI MENU</h3>
              <nav className="space-y-1">
                  <SidebarItem icon={<HomeIcon/>} label="Home" active={activeView === 'home'} onClick={() => setActiveView('home')} />
                  <SidebarItem icon={<CircleStackIcon/>} label="Products & Services" active={activeView === 'products'} onClick={() => setActiveView('products')} />
                  <SidebarItem icon={<BuildingOfficeIcon/>} label="Company & Culture" active={activeView === 'company'} onClick={() => setActiveView('company')} />
                  <SidebarItem icon={<UserGroupIcon/>} label="Human Resources" active={activeView === 'hr'} onClick={() => setActiveView('hr')} />
                  <SidebarItem icon={<BeakerIcon/>} label="R&D / Technology" active={activeView === 'rd'} onClick={() => setActiveView('rd')} />
                  <div className="border-t border-gray-200 my-2"></div>
                  <SidebarItem icon={<PencilIcon/>} label="Manage Content" active={activeView === 'manage'} onClick={() => setActiveView('manage')} />
              </nav>
          </aside>
          <main className="flex-1 overflow-y-auto p-6">
              {renderContent()}
          </main>
      </div>
      {viewingDocument && <DocumentViewerModal docName={viewingDocument} onClose={() => setViewingDocument(null)} />}
    </div>
  );
};

interface SidebarItemProps {
  icon: IconElement,
  label: string,
  active: boolean,
  onClick: () => void
}
const SidebarItem: React.FC<SidebarItemProps> = ({ icon, label, active, onClick}) => (
    <button onClick={onClick} className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors ${active ? 'bg-blue-50 text-blue-600' : 'text-gray-500 hover:bg-gray-100 hover:text-gray-900'}`}>
        <span className={active ? 'text-blue-600' : 'text-gray-400'}>{React.cloneElement(icon, { className: 'h-5 w-5' })}</span>
        {label}
    </button>
);


export default Wiki;