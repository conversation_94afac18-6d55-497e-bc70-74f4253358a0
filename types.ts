
export type TicketStatus = 'Received' | 'In Progress' | 'Waiting Feedback' | 'Done' | 'Canceled' | 'Closed' | 'Reopened';
export type TicketCategory = 'IT Support' | 'Finance' | 'Admin Request' | 'Marketing' | 'Human Resources' | 'Quality Assurance' | 'Operations' | 'R&D';
export type TicketUrgency = 'Low' | 'Medium' | 'High';

export interface User {
  id: string;
  name: string;
  role: 'CEO' | 'Manager' | 'Worker' | 'Salesperson' | 'QA Engineer' | 'Super User' | 'Technician' | 'Engineer' | 'Production Worker';
  department: 'Marketing' | 'IT' | 'Sales Team A' | 'Sales Team B' | 'Assembly' | 'Production' | 'Fabrication' | 'Finance' | 'Human Resources' | 'Quality Assurance' | 'System' | 'R&D' | 'Operations';
  status: 'Online' | 'On Business Trip' | 'On Leave';
  workload: {
    urgent: number;
    normal: number;
  };
  avatar: string;
  experience?: {
    itSupport?: number;
    finance?: number;
    adminRequest?: number;
    marketing?: number;
    humanResources?: number;
    qualityAssurance?: number;
    operations?: number;
    rnd?: number;
  };
}

export interface Activity {
  id:string;
  type: 'AI_SUMMARY' | 'COMMENT' | 'STATUS_CHANGE' | 'ASSIGNMENT' | 'CREATED' | 'AI_COMPLETION_REPORT';
  user?: Pick<User, 'id' | 'name' | 'avatar'>;
  timestamp: string;
  content: string;
}

export interface Ticket {
  id: string;
  title: string;
  description: string;
  status: TicketStatus;
  category: TicketCategory;
  urgency: TicketUrgency;
  assignees: User['id'][];
  createdAt: string;
  activity: Activity[];
  attachments?: { name: string; size: string }[];
}


export interface OrganizationNode {
    id: string;
    children?: OrganizationNode[];
}

export type View = string;
export type AppView = 'smart-form' | 'ai-assistant' | 'maintenance' | 'wiki';

export interface Message {
  id: string;
  sender: 'user' | 'ai';
  content: string | GroundedResponse | EnhancedRAGResponse;
}

export interface GroundedResponse {
    summary: string;
    source: string;
    quote: string;
}

// --- Form Types ---
export type FormFieldType = 'text' | 'textarea' | 'date' | 'select' | 'number' | 'file';

export interface FormField {
    name: string;
    label: string;
    type: FormFieldType;
    required: boolean;
    options?: string[]; // For select type
}

export interface FormTemplate {
    id: string;
    name: string;
    description: string;
    fields: FormField[];
}

// --- AI Service Types ---
export interface AnalyzedTicketInfo {
    title: string;
    category: TicketCategory;
    urgency: TicketUrgency;
    relatedDepartments: string[];
    suggestedAssignees: string[];
    workPlan: TicketWorkPlan;
}

export interface TicketWorkPlan {
    overview: string;
    resolution: string;
    workBreakdown: string[];
}

export interface CompletionReport {
    summaryOfResolution: string;
    identifiedRootCause: string;
    nextStepsOrRecommendations: string[];
}


// --- Maintenance Module Types ---
export interface SensorData {
    timestamp: string;
    value: number;
}
export interface Machine {
    id: string;
    name: string;
    zone: string;
    machineType: string;
    protocol: 'MQTT' | 'OPC-UA' | 'Modbus';
    status: 'Operational' | 'Warning' | 'Failure';
    temp: number;
    vibration: number;
    pressure: number;
    humidity: number;
    faultCode: string | null;
    healthScore: number;
    trends: {
        temperature: SensorData[];
        vibration: SensorData[];
        pressure: SensorData[];
        humidity: SensorData[];
    };
}
export interface Alert {
    id: string;
    machineName: string;
    message: string;
    timestamp: string;
    type: 'Warning' | 'Failure';
}


// --- Wiki Module Types ---
export type ProductCategory = 'VRV Systems' | 'Split/Multi-Split' | 'Applied/Chillers' | 'Air Purifiers';

export interface ProductManual {
    id: string;
    name: string;
    category: ProductCategory;
    modelNumbers: string[];
    summary: string;
    documentUrl: string; // Link to the PDF manual
}

export interface EmployeeDirectoryEntry {
    id: string; // Corresponds to User ID
    name: string;
    role: string;
    department: string;
    email: string;
    phone: string;
    location: string;
    avatar: string;
}

export interface WikiAttachment {
    id: string;
    name: string;
    type: string;
    size: number;
    url: string;
    dataType: 'structured' | 'unstructured' | 'analyzing';
    analysisResult?: {
        confidence: number;
        reasoning: string;
        detectedFormat?: string;
        structureElements?: string[];
    };
}

export interface KnowledgeArticle {
    id: string;
    title: string;
    category: 'R&D' | 'Engineering Best Practices' | 'Company Philosophy' | 'Market Analysis';
    content: string; // Changed from summary to full content
    author: string;
    authorDepartment: string;
    publishDate: string;
    tags: string[];
    attachments?: WikiAttachment[];
}

export interface Announcement {
    id: string;
    title: string;
    content: string;
    date: string;
    author: string;
    authorDepartment: string;
    priority?: 'Low' | 'Medium' | 'High';
    attachments?: WikiAttachment[];
}

export interface Policy {
    id: string;
    title: string;
    category: 'HR' | 'IT Security' | 'Finance' | 'General' | 'Operations';
    summary: string;
    documentUrl: string;
    effectiveDate: string;
    lastUpdated: string;
    author: string;
    authorDepartment: string;
    attachments?: WikiAttachment[];
}

export interface QuickLink {
    id: string;
    title: string;
    url?: string;
    appTarget?: AppView;
    icon: string; // Name of an icon component
}

// --- AI Assistant Types ---
export interface AIAssistantKnowledge {
    id: string;
    question: string;
    answer: string;
    source: string;
    quote: string;
    tags: string[];
    accessRoles: User['role'][];
    // Enhanced RAG properties
    documentId?: string;
    documentType?: 'manual' | 'policy' | 'knowledge_article' | 'expert_note' | 'historical_case';
    expertAuthor?: string;
    lastUpdated?: string;
    successRate?: number;
    relatedDocuments?: string[];
    confidenceScore?: number;
}

// Enhanced RAG Response Types
export interface RAGSource {
    document_id: string;
    relevance_score: number;
    expert_author?: string;
    success_rate?: string | number;
    last_updated: string;
    document_excerpt: string;
    document_type: 'manual' | 'policy' | 'knowledge_article' | 'expert_note' | 'historical_case';
    page_number?: number;
    section?: string;
    // Enhanced metadata
    document_url?: string;
    page_reference?: string;
    credibility_score?: number;
    related_documents?: string[];
    // Permission and experience metadata
    user_experience_score?: number;
    access_level?: string;
    permission_granted?: boolean;
    restricted_count?: number;
}

export interface EnhancedRAGResponse {
    answer: string;
    sources: RAGSource[];
    confidence_score: number;
    alternative_solutions?: number;
    query_processing_time?: number;
    knowledge_graph_connections?: string[];
    expert_recommendations?: ExpertRecommendation[];
    // Enhanced metadata
    search_strategy?: string;
    quality_indicators?: {
        source_diversity: boolean;
        expert_validated: boolean;
        recent_information: boolean;
        high_success_rate: boolean;
    };
    related_queries?: string[];
    synthesis_method?: 'single_source' | 'multi_source';
    document_coverage?: number;
}

export interface ExpertRecommendation {
    expert_id: string;
    expert_name: string;
    recommendation: string;
    contact_info?: string;
    video_url?: string;
    notes_url?: string;
}

// RAG Performance Metrics
export interface RAGMetrics {
    source_accuracy_rate: number;
    response_relevance_score: number;
    knowledge_coverage: number;
    expert_validation_rate: number;
    query_response_time: number;
    source_retrieval_speed: number;
    user_satisfaction_score: number;
    system_uptime: number;
}