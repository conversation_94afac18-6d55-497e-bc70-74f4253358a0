/**
 * Comprehensive Test Runner
 * Integrates all test suites: Enhanced RAG, Security/Compliance, Performance/Scalability
 */

import { EnhancedRAGTestRunner, ENHANCED_RAG_TEST_CASES } from './enhanced-rag-tests';
import { SecurityComplianceTestRunner, SECURITY_COMPLIANCE_TEST_CASES, COMPL<PERSON>NCE_TEST_CASES } from './security-compliance-tests';
import { PerformanceTestRunner, PERFORMANCE_TEST_CASES, SCALABILITY_TEST_CASES } from './performance-scalability-tests';
import type { TestResult, TestCase } from './enhanced-rag-tests';

// Comprehensive Test Report Interface
export interface ComprehensiveTestReport {
    summary: {
        totalTests: number;
        passedTests: number;
        failedTests: number;
        successRate: number;
        totalExecutionTime: number;
        testSuites: {
            [suiteName: string]: {
                tests: number;
                passed: number;
                failed: number;
                successRate: number;
            };
        };
    };
    detailedResults: {
        [suiteName: string]: TestResult[];
    };
    recommendations: string[];
    metrics: {
        performance: any;
        security: any;
        rag: any;
    };
}

// Main Test Runner Class
export class ComprehensiveTestRunner {
    private ragTestRunner = new EnhancedRAGTestRunner();
    private securityTestRunner = new SecurityComplianceTestRunner();
    private performanceTestRunner = new PerformanceTestRunner();

    async runAllTests(): Promise<ComprehensiveTestReport> {
        console.log('🚀 Starting Comprehensive AI Systems Test Suite');
        console.log('================================================');
        
        const startTime = Date.now();
        const results: { [suiteName: string]: TestResult[] } = {};

        // Run Enhanced RAG Tests
        console.log('\n📊 Running Enhanced RAG Tests...');
        results.ragTests = await this.ragTestRunner.runTestSuite(ENHANCED_RAG_TEST_CASES);
        
        // Run Security & Compliance Tests
        console.log('\n🔒 Running Security & Compliance Tests...');
        results.securityTests = await this.securityTestRunner.runSecurityTests();
        
        // Run Performance & Scalability Tests
        console.log('\n⚡ Running Performance & Scalability Tests...');
        results.performanceTests = await this.performanceTestRunner.runPerformanceTests();

        const totalExecutionTime = Date.now() - startTime;

        // Generate comprehensive report
        const report = this.generateComprehensiveReport(results, totalExecutionTime);
        
        console.log('\n📋 Test Suite Complete!');
        console.log('========================');
        console.log(this.formatSummaryReport(report));

        return report;
    }

    async runRAGDemoTests(): Promise<TestResult[]> {
        console.log('🎯 Running RAG Demo Test Suite');
        console.log('==============================');

        // Specific tests for demo scenarios
        const demoTests = ENHANCED_RAG_TEST_CASES.filter(test => 
            ['RAG-SV-001', 'RAG-SV-002', 'RAG-DI-001'].includes(test.id)
        );

        return await this.ragTestRunner.runTestSuite(demoTests);
    }

    async runSecurityAudit(): Promise<TestResult[]> {
        console.log('🛡️ Running Security Audit');
        console.log('=========================');

        return await this.securityTestRunner.runSecurityTests();
    }

    async runPerformanceBenchmark(): Promise<TestResult[]> {
        console.log('🏃‍♂️ Running Performance Benchmark');
        console.log('==================================');

        const benchmarkTests = PERFORMANCE_TEST_CASES.filter(test =>
            ['PERF-001', 'PERF-002'].includes(test.id)
        );

        return await this.performanceTestRunner.runPerformanceTests();
    }

    private generateComprehensiveReport(
        results: { [suiteName: string]: TestResult[] }, 
        totalExecutionTime: number
    ): ComprehensiveTestReport {
        const allResults = Object.values(results).flat();
        const totalTests = allResults.length;
        const passedTests = allResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        const successRate = (passedTests / totalTests) * 100;

        // Calculate suite-specific metrics
        const testSuites: { [suiteName: string]: any } = {};
        Object.entries(results).forEach(([suiteName, suiteResults]) => {
            const suitePassed = suiteResults.filter(r => r.passed).length;
            const suiteTotal = suiteResults.length;
            testSuites[suiteName] = {
                tests: suiteTotal,
                passed: suitePassed,
                failed: suiteTotal - suitePassed,
                successRate: (suitePassed / suiteTotal) * 100
            };
        });

        // Generate recommendations
        const recommendations = this.generateRecommendations(results);

        // Collect metrics
        const metrics = {
            performance: this.performanceTestRunner.getMetricsCollector().generateReport(),
            security: this.securityTestRunner.getAuditLog(),
            rag: this.calculateRAGMetrics(results.ragTests || [])
        };

        return {
            summary: {
                totalTests,
                passedTests,
                failedTests,
                successRate,
                totalExecutionTime,
                testSuites
            },
            detailedResults: results,
            recommendations,
            metrics
        };
    }

    private generateRecommendations(results: { [suiteName: string]: TestResult[] }): string[] {
        const recommendations: string[] = [];
        
        // Analyze RAG test results
        const ragResults = results.ragTests || [];
        const ragFailures = ragResults.filter(r => !r.passed);
        if (ragFailures.length > 0) {
            recommendations.push('🔍 RAG System: Consider improving source verification and confidence scoring mechanisms');
        }

        // Analyze security test results
        const securityResults = results.securityTests || [];
        const securityFailures = securityResults.filter(r => !r.passed);
        if (securityFailures.length > 0) {
            recommendations.push('🔒 Security: Strengthen access controls and audit trail implementation');
        }

        // Analyze performance test results
        const performanceResults = results.performanceTests || [];
        const performanceFailures = performanceResults.filter(r => !r.passed);
        if (performanceFailures.length > 0) {
            recommendations.push('⚡ Performance: Optimize query processing and implement better caching strategies');
        }

        // General recommendations
        if (recommendations.length === 0) {
            recommendations.push('✅ All test suites passed! Consider implementing continuous monitoring and automated testing');
        }

        recommendations.push('📈 Implement real-time performance monitoring dashboard');
        recommendations.push('🔄 Set up automated regression testing for CI/CD pipeline');
        recommendations.push('📊 Create user feedback collection system for RAG response quality');

        return recommendations;
    }

    private calculateRAGMetrics(ragResults: TestResult[]): any {
        const sourceVerificationTests = ragResults.filter(r => r.testCaseId.startsWith('RAG-SV'));
        const documentIntelligenceTests = ragResults.filter(r => r.testCaseId.startsWith('RAG-DI'));
        
        return {
            sourceVerificationSuccessRate: this.calculateSuccessRate(sourceVerificationTests),
            documentIntelligenceSuccessRate: this.calculateSuccessRate(documentIntelligenceTests),
            averageResponseTime: this.calculateAverageResponseTime(ragResults),
            totalRAGTests: ragResults.length
        };
    }

    private calculateSuccessRate(results: TestResult[]): number {
        if (results.length === 0) return 0;
        const passed = results.filter(r => r.passed).length;
        return (passed / results.length) * 100;
    }

    private calculateAverageResponseTime(results: TestResult[]): number {
        if (results.length === 0) return 0;
        const totalTime = results.reduce((sum, r) => sum + r.executionTime, 0);
        return totalTime / results.length;
    }

    private formatSummaryReport(report: ComprehensiveTestReport): string {
        const { summary } = report;
        
        return `
📊 TEST EXECUTION SUMMARY
========================
Total Tests: ${summary.totalTests}
✅ Passed: ${summary.passedTests}
❌ Failed: ${summary.failedTests}
📈 Success Rate: ${summary.successRate.toFixed(1)}%
⏱️ Total Time: ${(summary.totalExecutionTime / 1000).toFixed(2)}s

📋 SUITE BREAKDOWN
==================
${Object.entries(summary.testSuites).map(([suite, stats]) => 
    `${suite}: ${stats.passed}/${stats.tests} (${stats.successRate.toFixed(1)}%)`
).join('\n')}

💡 KEY RECOMMENDATIONS
=====================
${report.recommendations.map(rec => `• ${rec}`).join('\n')}
        `;
    }

    // Export test results to different formats
    async exportResults(report: ComprehensiveTestReport, format: 'json' | 'csv' | 'html' = 'json'): Promise<string> {
        switch (format) {
            case 'json':
                return JSON.stringify(report, null, 2);
            case 'csv':
                return this.convertToCSV(report);
            case 'html':
                return this.convertToHTML(report);
            default:
                return JSON.stringify(report, null, 2);
        }
    }

    private convertToCSV(report: ComprehensiveTestReport): string {
        const headers = ['Suite', 'Test ID', 'Test Name', 'Status', 'Execution Time (ms)', 'Error'];
        const rows: string[] = [headers.join(',')];

        Object.entries(report.detailedResults).forEach(([suite, results]) => {
            results.forEach(result => {
                const row = [
                    suite,
                    result.testCaseId,
                    `"${result.testCaseId}"`, // Test name would need to be looked up
                    result.passed ? 'PASS' : 'FAIL',
                    result.executionTime.toString(),
                    result.errors ? `"${result.errors.join('; ')}"` : ''
                ];
                rows.push(row.join(','));
            });
        });

        return rows.join('\n');
    }

    private convertToHTML(report: ComprehensiveTestReport): string {
        return `
<!DOCTYPE html>
<html>
<head>
    <title>AI Systems Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .suite { margin-bottom: 30px; }
        .test-result { margin: 5px 0; padding: 10px; border-radius: 3px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🚀 AI Systems Comprehensive Test Report</h1>
    
    <div class="summary">
        <h2>📊 Summary</h2>
        <p><strong>Total Tests:</strong> ${report.summary.totalTests}</p>
        <p><strong>Success Rate:</strong> ${report.summary.successRate.toFixed(1)}%</p>
        <p><strong>Execution Time:</strong> ${(report.summary.totalExecutionTime / 1000).toFixed(2)}s</p>
    </div>

    <h2>💡 Recommendations</h2>
    <ul>
        ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
    </ul>

    <h2>📋 Detailed Results</h2>
    ${Object.entries(report.detailedResults).map(([suite, results]) => `
        <div class="suite">
            <h3>${suite}</h3>
            ${results.map(result => `
                <div class="test-result ${result.passed ? 'pass' : 'fail'}">
                    <strong>${result.testCaseId}</strong> - ${result.passed ? 'PASS' : 'FAIL'} 
                    (${result.executionTime}ms)
                    ${result.errors ? `<br><small>Errors: ${result.errors.join(', ')}</small>` : ''}
                </div>
            `).join('')}
        </div>
    `).join('')}
</body>
</html>
        `;
    }
}

// Utility function to run quick demo tests
export async function runQuickDemo(): Promise<void> {
    const runner = new ComprehensiveTestRunner();
    
    console.log('🎯 Running Quick Demo Tests');
    console.log('===========================');
    
    const demoResults = await runner.runRAGDemoTests();
    
    console.log('\n📊 Demo Results:');
    demoResults.forEach(result => {
        console.log(`${result.testCaseId}: ${result.passed ? '✅ PASS' : '❌ FAIL'} (${result.executionTime}ms)`);
        if (!result.passed && result.errors) {
            console.log(`   Errors: ${result.errors.join(', ')}`);
        }
    });
}

// Export for use in other modules
export { ComprehensiveTestRunner as default };
