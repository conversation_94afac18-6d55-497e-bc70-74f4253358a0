import React, { useState, useEffect } from 'react';
import { WikiAttachment } from '../types';
import { 
    DocumentIcon, 
    ChartBarIcon, 
    CpuChipIcon, 
    CheckCircleIcon, 
    ExclamationTriangleIcon,
    ClockIcon,
    EyeIcon
} from './icons/Icons';

interface AttachmentAnalysisWorkflowProps {
    file: File;
    onAnalysisComplete: (attachment: WikiAttachment) => void;
    onCancel: () => void;
}

interface AnalysisStep {
    id: string;
    name: string;
    status: 'pending' | 'processing' | 'completed' | 'error';
    description: string;
    duration?: number;
}

const AttachmentAnalysisWorkflow: React.FC<AttachmentAnalysisWorkflowProps> = ({
    file,
    onAnalysisComplete,
    onCancel
}) => {
    const [currentStep, setCurrentStep] = useState(0);
    const [analysisSteps, setAnalysisSteps] = useState<AnalysisStep[]>([
        {
            id: 'upload',
            name: 'File Upload',
            status: 'completed',
            description: 'File successfully uploaded and validated'
        },
        {
            id: 'preprocessing',
            name: 'Preprocessing',
            status: 'processing',
            description: 'Extracting metadata and file characteristics'
        },
        {
            id: 'content-analysis',
            name: 'Content Analysis',
            status: 'pending',
            description: 'Analyzing document structure and content patterns'
        },
        {
            id: 'classification',
            name: 'Data Classification',
            status: 'pending',
            description: 'Determining if data is structured or unstructured'
        },
        {
            id: 'validation',
            name: 'Validation',
            status: 'pending',
            description: 'Verifying classification results and confidence score'
        }
    ]);

    const [analysisResult, setAnalysisResult] = useState<WikiAttachment | null>(null);

    useEffect(() => {
        const runAnalysis = async () => {
            // Simulate analysis workflow
            for (let i = 1; i < analysisSteps.length; i++) {
                await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));
                
                setAnalysisSteps(prev => prev.map((step, index) => {
                    if (index === i - 1) {
                        return { ...step, status: 'completed', duration: Math.floor(1500 + Math.random() * 1000) };
                    }
                    if (index === i) {
                        return { ...step, status: 'processing' };
                    }
                    return step;
                }));
                setCurrentStep(i);
            }

            // Complete final step and generate result
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const finalResult = generateAnalysisResult(file);
            setAnalysisResult(finalResult);
            
            setAnalysisSteps(prev => prev.map((step, index) => {
                if (index === analysisSteps.length - 1) {
                    return { ...step, status: 'completed', duration: 2000 };
                }
                return step;
            }));
        };

        runAnalysis();
    }, [file]);

    const generateAnalysisResult = (file: File): WikiAttachment => {
        const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';
        
        // Simulate intelligent classification
        const structuredFormats = ['csv', 'xlsx', 'xls', 'json', 'xml', 'sql', 'tsv'];
        const unstructuredFormats = ['pdf', 'doc', 'docx', 'txt', 'md', 'rtf'];
        
        let dataType: 'structured' | 'unstructured';
        let confidence: number;
        let reasoning: string;
        let detectedFormat: string;
        let structureElements: string[] = [];

        if (structuredFormats.includes(fileExtension)) {
            dataType = 'structured';
            confidence = 0.85 + Math.random() * 0.1;
            reasoning = `File format .${fileExtension} typically contains structured data with defined schemas and relationships.`;
            detectedFormat = fileExtension.toUpperCase();
            structureElements = ['Tables', 'Columns', 'Rows', 'Headers', 'Data Types'];
        } else if (unstructuredFormats.includes(fileExtension)) {
            dataType = 'unstructured';
            confidence = 0.80 + Math.random() * 0.15;
            reasoning = `File format .${fileExtension} typically contains unstructured text data without predefined schema.`;
            detectedFormat = fileExtension.toUpperCase();
            structureElements = ['Paragraphs', 'Headings', 'Free Text', 'Images'];
        } else {
            // Mixed or unknown format
            dataType = Math.random() > 0.5 ? 'structured' : 'unstructured';
            confidence = 0.60 + Math.random() * 0.2;
            reasoning = `File format .${fileExtension} requires content analysis to determine structure type.`;
            detectedFormat = fileExtension.toUpperCase();
        }

        return {
            id: `att-${Date.now()}`,
            name: file.name,
            type: file.type || 'application/octet-stream',
            size: file.size,
            url: URL.createObjectURL(file),
            dataType,
            analysisResult: {
                confidence: Math.round(confidence * 100) / 100,
                reasoning,
                detectedFormat,
                structureElements
            }
        };
    };

    const getStepIcon = (step: AnalysisStep) => {
        switch (step.status) {
            case 'completed':
                return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
            case 'processing':
                return <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
            case 'error':
                return <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />;
            default:
                return <ClockIcon className="w-5 h-5 text-gray-400" />;
        }
    };

    const getStepColor = (step: AnalysisStep) => {
        switch (step.status) {
            case 'completed':
                return 'border-green-200 bg-green-50';
            case 'processing':
                return 'border-blue-200 bg-blue-50';
            case 'error':
                return 'border-red-200 bg-red-50';
            default:
                return 'border-gray-200 bg-gray-50';
        }
    };

    return (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                                <CpuChipIcon className="w-6 h-6 text-blue-600" />
                                Intelligent Attachment Analysis
                            </h3>
                            <p className="text-gray-600 mt-1">Analyzing: {file.name}</p>
                        </div>
                        <button
                            onClick={onCancel}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            ✕
                        </button>
                    </div>
                </div>

                <div className="p-6">
                    {/* File Info */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-6">
                        <div className="flex items-center gap-3">
                            <DocumentIcon className="w-8 h-8 text-blue-600" />
                            <div>
                                <p className="font-medium text-gray-900">{file.name}</p>
                                <p className="text-sm text-gray-600">
                                    {(file.size / 1024).toFixed(1)} KB • {file.type || 'Unknown type'}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Analysis Steps */}
                    <div className="space-y-4 mb-6">
                        <h4 className="font-semibold text-gray-900">Analysis Progress</h4>
                        {analysisSteps.map((step, index) => (
                            <div
                                key={step.id}
                                className={`border rounded-lg p-4 transition-all ${getStepColor(step)}`}
                            >
                                <div className="flex items-center gap-3">
                                    {getStepIcon(step)}
                                    <div className="flex-1">
                                        <div className="flex items-center justify-between">
                                            <h5 className="font-medium text-gray-900">{step.name}</h5>
                                            {step.duration && (
                                                <span className="text-xs text-gray-500">
                                                    {step.duration}ms
                                                </span>
                                            )}
                                        </div>
                                        <p className="text-sm text-gray-600">{step.description}</p>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Analysis Results */}
                    {analysisResult && (
                        <div className="bg-white border border-gray-200 rounded-lg p-6">
                            <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                                <ChartBarIcon className="w-5 h-5 text-green-600" />
                                Analysis Results
                            </h4>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Data Classification
                                        </label>
                                        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                            analysisResult.dataType === 'structured' 
                                                ? 'bg-blue-100 text-blue-800' 
                                                : 'bg-purple-100 text-purple-800'
                                        }`}>
                                            {analysisResult.dataType === 'structured' ? '📊 Structured Data' : '📄 Unstructured Data'}
                                        </div>
                                    </div>

                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Confidence Score
                                        </label>
                                        <div className="flex items-center gap-2">
                                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                                                <div 
                                                    className="bg-green-500 h-2 rounded-full transition-all"
                                                    style={{ width: `${(analysisResult.analysisResult?.confidence || 0) * 100}%` }}
                                                />
                                            </div>
                                            <span className="text-sm font-medium text-gray-900">
                                                {((analysisResult.analysisResult?.confidence || 0) * 100).toFixed(1)}%
                                            </span>
                                        </div>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Detected Format
                                        </label>
                                        <p className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded">
                                            {analysisResult.analysisResult?.detectedFormat}
                                        </p>
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Analysis Reasoning
                                    </label>
                                    <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                                        {analysisResult.analysisResult?.reasoning}
                                    </p>

                                    {analysisResult.analysisResult?.structureElements && (
                                        <div className="mt-4">
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Detected Elements
                                            </label>
                                            <div className="flex flex-wrap gap-2">
                                                {analysisResult.analysisResult.structureElements.map((element, index) => (
                                                    <span
                                                        key={index}
                                                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                                                    >
                                                        {element}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div className="flex justify-end gap-3 mt-6">
                                <button
                                    onClick={onCancel}
                                    className="px-4 py-2 text-gray-600 hover:text-gray-800"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={() => onAnalysisComplete(analysisResult)}
                                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
                                >
                                    <CheckCircleIcon className="w-4 h-4" />
                                    Accept & Upload
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default AttachmentAnalysisWorkflow;
