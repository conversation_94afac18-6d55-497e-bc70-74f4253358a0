@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 Daikin AI System - TD_Dev Launcher
echo ========================================
echo.

echo 💡 Starting main console from TD_Dev folder...
echo.

:: Check if TD_Dev folder exists
if not exist "TD_Dev" (
    echo ❌ Error: TD_Dev folder does not exist
    echo Please ensure TD_Dev folder is in current directory
    pause
    exit /b 1
)

:: Check if main menu script exists
if not exist "TD_Dev\main-menu.bat" (
    echo ❌ Error: Main menu script does not exist
    echo Please ensure TD_Dev\main-menu.bat file exists
    pause
    exit /b 1
)

echo ✅ Found TD_Dev console
echo 🚀 Starting main console...
echo.

:: Start main console
call "TD_Dev\main-menu.bat"

echo.
echo 👋 Thank you for using Daikin AI System TD_Dev console
pause
