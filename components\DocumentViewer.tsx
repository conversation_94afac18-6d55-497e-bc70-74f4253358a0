/**
 * Interactive Document Viewer
 * Displays source documents with highlighting and metadata
 */

import React, { useState } from 'react';
import type { RAGSource } from '../types';
import { 
    XMarkIcon, 
    BookOpenIcon, 
    UserIcon, 
    ClockIcon, 
    CheckCircleIcon,
    ExclamationTriangleIcon,
    ArrowRightIcon
} from './icons/Icons';

interface DocumentViewerProps {
    source: RAGSource;
    onClose: () => void;
    highlightText?: string;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({ source, onClose, highlightText }) => {
    const [activeTab, setActiveTab] = useState<'content' | 'metadata'>('content');

    // Simulate document content based on source
    const generateDocumentContent = (source: RAGSource): string => {
        const baseContent = source.document_excerpt;
        
        // Expand content based on document type
        switch (source.document_type) {
            case 'manual':
                return `# Technical Manual - ${source.section}

## Overview
${baseContent}

## Detailed Procedures
1. **Preparation Phase**
   - Ensure all safety equipment is available
   - Review the technical specifications
   - Verify system compatibility

2. **Implementation Steps**
   - Follow the documented procedures carefully
   - Monitor system parameters during operation
   - Document any deviations or issues

3. **Verification**
   - Perform system checks
   - Validate performance metrics
   - Update maintenance logs

## Safety Considerations
Always follow proper safety protocols when working with this equipment. Refer to the safety manual for detailed guidelines.

## Related Documents
- Safety Protocol Manual
- Troubleshooting Guide
- Maintenance Schedule

---
*Document ID: ${source.document_id}*
*Last Updated: ${source.last_updated}*
*Author: ${source.expert_author}*`;

            case 'policy':
                return `# Company Policy - ${source.section}

## Policy Statement
${baseContent}

## Scope and Application
This policy applies to all employees and contractors working within the organization. Compliance is mandatory and subject to regular review.

## Procedures
1. **Initial Assessment**
   - Review current practices
   - Identify areas for improvement
   - Document compliance status

2. **Implementation**
   - Follow established procedures
   - Maintain proper documentation
   - Report any issues immediately

## Compliance Requirements
All personnel must adhere to this policy. Regular training and updates will be provided to ensure continued compliance.

## Review and Updates
This policy is reviewed annually and updated as necessary to reflect current best practices and regulatory requirements.

---
*Document ID: ${source.document_id}*
*Effective Date: ${source.last_updated}*
*Policy Owner: ${source.expert_author}*`;

            case 'knowledge_article':
                return `# Knowledge Article - ${source.section}

## Summary
${baseContent}

## Detailed Information
This knowledge article provides comprehensive information based on expert analysis and field experience. The content has been validated through practical application and peer review.

## Key Points
- Evidence-based recommendations
- Field-tested procedures
- Expert validation and approval
- Regular updates based on new findings

## Application Guidelines
When applying this knowledge, consider the specific context and requirements of your situation. Consult with subject matter experts if additional clarification is needed.

## Expert Insights
"${baseContent}" - This insight reflects years of practical experience and has been validated through multiple successful implementations.

## Related Topics
- Best practices in the field
- Common troubleshooting scenarios
- Advanced techniques and methods

---
*Article ID: ${source.document_id}*
*Published: ${source.last_updated}*
*Expert Author: ${source.expert_author}*
*Success Rate: ${source.success_rate}*`;

            default:
                return `# Document - ${source.section}

${baseContent}

This document contains important information relevant to your query. Please review the content carefully and consult with appropriate personnel if you have questions.

---
*Document ID: ${source.document_id}*
*Last Updated: ${source.last_updated}*`;
        }
    };

    const highlightContent = (content: string, highlight?: string): string => {
        if (!highlight) return content;
        
        const regex = new RegExp(`(${highlight})`, 'gi');
        return content.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
    };

    const getDocumentTypeIcon = (type: string) => {
        switch (type) {
            case 'manual': return <BookOpenIcon className="w-5 h-5 text-blue-600" />;
            case 'policy': return <ExclamationTriangleIcon className="w-5 h-5 text-orange-600" />;
            case 'knowledge_article': return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
            default: return <BookOpenIcon className="w-5 h-5 text-gray-600" />;
        }
    };

    const getCredibilityColor = (score?: number): string => {
        if (!score) return 'text-gray-500';
        if (score >= 0.9) return 'text-green-600';
        if (score >= 0.7) return 'text-yellow-600';
        return 'text-red-600';
    };

    const documentContent = generateDocumentContent(source);

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                    <div className="flex items-center gap-3">
                        {getDocumentTypeIcon(source.document_type)}
                        <div>
                            <h2 className="text-xl font-bold text-gray-900">{source.section}</h2>
                            <p className="text-sm text-gray-500">Document ID: {source.document_id}</p>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                        <XMarkIcon className="w-5 h-5 text-gray-500" />
                    </button>
                </div>

                {/* Tab Navigation */}
                <div className="flex border-b border-gray-200">
                    <button
                        onClick={() => setActiveTab('content')}
                        className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
                            activeTab === 'content'
                                ? 'border-blue-600 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                    >
                        Document Content
                    </button>
                    <button
                        onClick={() => setActiveTab('metadata')}
                        className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
                            activeTab === 'metadata'
                                ? 'border-blue-600 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                    >
                        Metadata & Analytics
                    </button>
                </div>

                {/* Content */}
                <div className="flex-1 overflow-y-auto p-6">
                    {activeTab === 'content' ? (
                        <div className="prose prose-sm max-w-none">
                            <div 
                                dangerouslySetInnerHTML={{ 
                                    __html: highlightContent(documentContent, highlightText)
                                        .replace(/\n/g, '<br>')
                                        .replace(/#{1,3}\s(.+)/g, '<h3 class="text-lg font-semibold text-gray-900 mt-4 mb-2">$1</h3>')
                                        .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
                                        .replace(/\*(.+?)\*/g, '<em>$1</em>')
                                }} 
                            />
                        </div>
                    ) : (
                        <div className="space-y-6">
                            {/* Source Metadata */}
                            <div className="bg-gray-50 rounded-lg p-4">
                                <h3 className="font-semibold text-gray-900 mb-3">Source Information</h3>
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div className="flex items-center gap-2">
                                        <UserIcon className="w-4 h-4 text-gray-500" />
                                        <span className="text-gray-600">Author:</span>
                                        <span className="font-medium">{source.expert_author || 'System Generated'}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <ClockIcon className="w-4 h-4 text-gray-500" />
                                        <span className="text-gray-600">Last Updated:</span>
                                        <span className="font-medium">{source.last_updated}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <CheckCircleIcon className="w-4 h-4 text-gray-500" />
                                        <span className="text-gray-600">Success Rate:</span>
                                        <span className="font-medium">{source.success_rate}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <BookOpenIcon className="w-4 h-4 text-gray-500" />
                                        <span className="text-gray-600">Page Reference:</span>
                                        <span className="font-medium">{source.page_reference || 'N/A'}</span>
                                    </div>
                                </div>
                            </div>

                            {/* Quality Indicators */}
                            <div className="bg-blue-50 rounded-lg p-4">
                                <h3 className="font-semibold text-gray-900 mb-3">Quality Indicators</h3>
                                <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Relevance Score</span>
                                        <div className="flex items-center gap-2">
                                            <div className="w-24 bg-gray-200 rounded-full h-2">
                                                <div 
                                                    className="bg-blue-600 h-2 rounded-full" 
                                                    style={{ width: `${source.relevance_score * 100}%` }}
                                                ></div>
                                            </div>
                                            <span className="text-sm font-medium">{Math.round(source.relevance_score * 100)}%</span>
                                        </div>
                                    </div>
                                    {source.credibility_score && (
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm text-gray-600">Credibility Score</span>
                                            <div className="flex items-center gap-2">
                                                <div className="w-24 bg-gray-200 rounded-full h-2">
                                                    <div 
                                                        className={`h-2 rounded-full ${
                                                            source.credibility_score >= 0.9 ? 'bg-green-600' :
                                                            source.credibility_score >= 0.7 ? 'bg-yellow-600' : 'bg-red-600'
                                                        }`}
                                                        style={{ width: `${source.credibility_score * 100}%` }}
                                                    ></div>
                                                </div>
                                                <span className={`text-sm font-medium ${getCredibilityColor(source.credibility_score)}`}>
                                                    {Math.round(source.credibility_score * 100)}%
                                                </span>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Related Documents */}
                            {source.related_documents && source.related_documents.length > 0 && (
                                <div className="bg-green-50 rounded-lg p-4">
                                    <h3 className="font-semibold text-gray-900 mb-3">Related Documents</h3>
                                    <div className="space-y-2">
                                        {source.related_documents.map((docId, index) => (
                                            <div key={index} className="flex items-center gap-2 text-sm">
                                                <ArrowRightIcon className="w-4 h-4 text-gray-400" />
                                                <span className="text-blue-600 hover:text-blue-800 cursor-pointer">
                                                    {docId}
                                                </span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
                    <div className="text-sm text-gray-500">
                        Document Type: <span className="font-medium capitalize">{source.document_type.replace('_', ' ')}</span>
                    </div>
                    <button
                        onClick={onClose}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
};

export default DocumentViewer;
